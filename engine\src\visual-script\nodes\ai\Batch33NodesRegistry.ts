/**
 * 批次3.3节点注册表
 * 注册所有新开发的25个AI节点
 */

// AI工具节点导入
import {
  ModelDeploymentNode,
  ModelMonitoringNode,
  ModelVersioningNode
} from './AIToolNodes';

import {
  AutoMLNode,
  ExplainableAINode,
  AIEthicsNode,
  ModelCompressionNode,
  QuantizationNode
} from './AIToolNodes2';

import {
  PruningNode,
  DistillationNode
} from './AIToolNodes3';

// 计算机视觉节点导入
import {
  ImageSegmentationNode,
  ObjectTrackingNode,
  FaceRecognitionNode,
  OpticalCharacterRecognitionNode
} from './ComputerVisionNodes2';

import {
  ImageGenerationNode,
  StyleTransferNode,
  ImageEnhancementNode,
  AugmentedRealityNode
} from './ComputerVisionNodes3';

// 自然语言处理节点导入
import {
  TextClassificationNode,
  NamedEntityRecognitionNode,
  SentimentAnalysisNode,
  TextSummarizationNode
} from './NaturalLanguageProcessingNodes';

import {
  MachineTranslationNode,
  QuestionAnsweringNode,
  TextGenerationNode
} from './NaturalLanguageProcessingNodes2';

/**
 * 批次3.3节点注册表类
 */
export class Batch33NodesRegistry {
  private static instance: Batch33NodesRegistry;
  private nodeTypes: Map<string, any> = new Map();
  private nodeCategories: Map<string, string[]> = new Map();

  private constructor() {
    this.registerAllNodes();
  }

  public static getInstance(): Batch33NodesRegistry {
    if (!Batch33NodesRegistry.instance) {
      Batch33NodesRegistry.instance = new Batch33NodesRegistry();
    }
    return Batch33NodesRegistry.instance;
  }

  /**
   * 注册所有批次3.3节点
   */
  private registerAllNodes(): void {
    this.registerAIToolNodes();
    this.registerComputerVisionNodes();
    this.registerNLPNodes();
    this.setupCategories();
  }

  /**
   * 注册AI工具节点
   */
  private registerAIToolNodes(): void {
    // AI工具节点 (10个)
    this.nodeTypes.set('ModelDeployment', ModelDeploymentNode);
    this.nodeTypes.set('ModelMonitoring', ModelMonitoringNode);
    this.nodeTypes.set('ModelVersioning', ModelVersioningNode);
    this.nodeTypes.set('AutoML', AutoMLNode);
    this.nodeTypes.set('ExplainableAI', ExplainableAINode);
    this.nodeTypes.set('AIEthics', AIEthicsNode);
    this.nodeTypes.set('ModelCompression', ModelCompressionNode);
    this.nodeTypes.set('Quantization', QuantizationNode);
    this.nodeTypes.set('Pruning', PruningNode);
    this.nodeTypes.set('Distillation', DistillationNode);
  }

  /**
   * 注册计算机视觉节点
   */
  private registerComputerVisionNodes(): void {
    // 计算机视觉节点 (8个)
    this.nodeTypes.set('ImageSegmentation', ImageSegmentationNode);
    this.nodeTypes.set('ObjectTracking', ObjectTrackingNode);
    this.nodeTypes.set('FaceRecognition', FaceRecognitionNode);
    this.nodeTypes.set('OpticalCharacterRecognition', OpticalCharacterRecognitionNode);
    this.nodeTypes.set('ImageGeneration', ImageGenerationNode);
    this.nodeTypes.set('StyleTransfer', StyleTransferNode);
    this.nodeTypes.set('ImageEnhancement', ImageEnhancementNode);
    this.nodeTypes.set('AugmentedReality', AugmentedRealityNode);
  }

  /**
   * 注册自然语言处理节点
   */
  private registerNLPNodes(): void {
    // 自然语言处理节点 (7个)
    this.nodeTypes.set('TextClassification', TextClassificationNode);
    this.nodeTypes.set('NamedEntityRecognition', NamedEntityRecognitionNode);
    this.nodeTypes.set('SentimentAnalysis', SentimentAnalysisNode);
    this.nodeTypes.set('TextSummarization', TextSummarizationNode);
    this.nodeTypes.set('MachineTranslation', MachineTranslationNode);
    this.nodeTypes.set('QuestionAnswering', QuestionAnsweringNode);
    this.nodeTypes.set('TextGeneration', TextGenerationNode);
  }

  /**
   * 设置节点分类
   */
  private setupCategories(): void {
    this.nodeCategories.set('AI工具', [
      'ModelDeployment',
      'ModelMonitoring', 
      'ModelVersioning',
      'AutoML',
      'ExplainableAI',
      'AIEthics',
      'ModelCompression',
      'Quantization',
      'Pruning',
      'Distillation'
    ]);

    this.nodeCategories.set('计算机视觉', [
      'ImageSegmentation',
      'ObjectTracking',
      'FaceRecognition',
      'OpticalCharacterRecognition',
      'ImageGeneration',
      'StyleTransfer',
      'ImageEnhancement',
      'AugmentedReality'
    ]);

    this.nodeCategories.set('自然语言处理', [
      'TextClassification',
      'NamedEntityRecognition',
      'SentimentAnalysis',
      'TextSummarization',
      'MachineTranslation',
      'QuestionAnswering',
      'TextGeneration'
    ]);
  }

  /**
   * 获取节点类型
   */
  public getNodeType(typeName: string): any {
    return this.nodeTypes.get(typeName);
  }

  /**
   * 获取所有节点类型
   */
  public getAllNodeTypes(): Map<string, any> {
    return new Map(this.nodeTypes);
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): Map<string, string[]> {
    return new Map(this.nodeCategories);
  }

  /**
   * 获取指定分类的节点
   */
  public getNodesByCategory(category: string): string[] {
    return this.nodeCategories.get(category) || [];
  }

  /**
   * 检查节点是否存在
   */
  public hasNodeType(typeName: string): boolean {
    return this.nodeTypes.has(typeName);
  }

  /**
   * 创建节点实例
   */
  public createNode(typeName: string): any {
    const NodeClass = this.nodeTypes.get(typeName);
    if (!NodeClass) {
      throw new Error(`未找到节点类型: ${typeName}`);
    }
    return new NodeClass();
  }

  /**
   * 获取节点统计信息
   */
  public getStatistics(): any {
    const stats = {
      totalNodes: this.nodeTypes.size,
      categories: {},
      nodeList: Array.from(this.nodeTypes.keys())
    };

    for (const [category, nodes] of this.nodeCategories.entries()) {
      (stats.categories as any)[category] = nodes.length;
    }

    return stats;
  }

  /**
   * 验证所有节点
   */
  public validateAllNodes(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    for (const [typeName, NodeClass] of this.nodeTypes.entries()) {
      try {
        const instance = new NodeClass();
        
        // 检查基本属性
        if (!instance.nodeType) {
          errors.push(`节点 ${typeName} 缺少 nodeType 属性`);
        }
        
        if (!instance.name) {
          errors.push(`节点 ${typeName} 缺少 name 属性`);
        }
        
        // 检查execute方法
        if (typeof instance.execute !== 'function') {
          errors.push(`节点 ${typeName} 缺少 execute 方法`);
        }
        
      } catch (error) {
        errors.push(`节点 ${typeName} 实例化失败: ${error}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// 导出单例实例
export const batch33NodesRegistry = Batch33NodesRegistry.getInstance();

// 导出所有节点类型常量
export const BATCH33_NODE_TYPES = {
  // AI工具节点
  MODEL_DEPLOYMENT: 'ModelDeployment',
  MODEL_MONITORING: 'ModelMonitoring',
  MODEL_VERSIONING: 'ModelVersioning',
  AUTO_ML: 'AutoML',
  EXPLAINABLE_AI: 'ExplainableAI',
  AI_ETHICS: 'AIEthics',
  MODEL_COMPRESSION: 'ModelCompression',
  QUANTIZATION: 'Quantization',
  PRUNING: 'Pruning',
  DISTILLATION: 'Distillation',
  
  // 计算机视觉节点
  IMAGE_SEGMENTATION: 'ImageSegmentation',
  OBJECT_TRACKING: 'ObjectTracking',
  FACE_RECOGNITION: 'FaceRecognition',
  OPTICAL_CHARACTER_RECOGNITION: 'OpticalCharacterRecognition',
  IMAGE_GENERATION: 'ImageGeneration',
  STYLE_TRANSFER: 'StyleTransfer',
  IMAGE_ENHANCEMENT: 'ImageEnhancement',
  AUGMENTED_REALITY: 'AugmentedReality',
  
  // 自然语言处理节点
  TEXT_CLASSIFICATION: 'TextClassification',
  NAMED_ENTITY_RECOGNITION: 'NamedEntityRecognition',
  SENTIMENT_ANALYSIS: 'SentimentAnalysis',
  TEXT_SUMMARIZATION: 'TextSummarization',
  MACHINE_TRANSLATION: 'MachineTranslation',
  QUESTION_ANSWERING: 'QuestionAnswering',
  TEXT_GENERATION: 'TextGeneration'
} as const;

// 导出节点分类常量
export const BATCH33_CATEGORIES = {
  AI_TOOLS: 'AI工具',
  COMPUTER_VISION: '计算机视觉',
  NATURAL_LANGUAGE_PROCESSING: '自然语言处理'
} as const;
