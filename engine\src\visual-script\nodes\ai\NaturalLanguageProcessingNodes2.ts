/**
 * 自然语言处理节点实现 - 第二部分
 * 完成批次3.3中剩余的自然语言处理节点
 */

import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { TranslationResult, QuestionAnsweringResult, TextGenerationResult } from './NaturalLanguageProcessingNodes';

/**
 * 机器翻译节点
 * 将文本从一种语言翻译为另一种语言
 */
export class MachineTranslationNode extends VisualScriptNode {
  constructor() {
    super('MachineTranslation', '机器翻译');
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('text', 'string', '输入文本', '');
    this.addInput('sourceLanguage', 'string', '源语言', 'auto');
    this.addInput('targetLanguage', 'string', '目标语言', 'en');
    this.addInput('modelId', 'string', '翻译模型ID', '');
    this.addInput('domain', 'string', '领域', 'general'); // general, medical, legal, technical
    this.addInput('generateAlternatives', 'boolean', '生成备选', false);
  }

  private setupOutputs(): void {
    this.addOutput('translatedText', 'string', '翻译文本');
    this.addOutput('detectedLanguage', 'string', '检测语言');
    this.addOutput('confidence', 'number', '翻译置信度');
    this.addOutput('alternatives', 'array', '备选翻译');
    this.addOutput('onTranslated', 'boolean', '翻译完成');
    this.addOutput('onError', 'boolean', '翻译失败');
  }

  public execute(inputs?: any): any {
    try {
      const text = inputs?.text || this.getInputValue('text');
      const sourceLanguage = inputs?.sourceLanguage || this.getInputValue('sourceLanguage');
      const targetLanguage = inputs?.targetLanguage || this.getInputValue('targetLanguage');
      const modelId = inputs?.modelId || this.getInputValue('modelId');
      const domain = inputs?.domain || this.getInputValue('domain');
      const generateAlternatives = inputs?.generateAlternatives !== undefined ? inputs.generateAlternatives : this.getInputValue('generateAlternatives');

      if (!text) {
        throw new Error('输入文本不能为空');
      }

      if (!targetLanguage) {
        throw new Error('目标语言不能为空');
      }

      Debug.log('MachineTranslationNode', `开始翻译: ${sourceLanguage} -> ${targetLanguage}`);

      const result = this.translateText(text, sourceLanguage, targetLanguage, modelId, domain, generateAlternatives);

      Debug.log('MachineTranslationNode', `翻译完成，置信度: ${result.confidence}`);

      return {
        translatedText: result.translatedText,
        detectedLanguage: result.sourceLanguage,
        confidence: result.confidence,
        alternatives: result.alternatives || [],
        onTranslated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('MachineTranslationNode', '翻译失败:', error);
      return {
        translatedText: '',
        detectedLanguage: '',
        confidence: 0,
        alternatives: [],
        onTranslated: false,
        onError: true
      };
    }
  }

  private translateText(text: string, sourceLang: string, targetLang: string, modelId: string, domain: string, generateAlternatives: boolean): TranslationResult {
    // 模拟语言检测
    const detectedLanguage = sourceLang === 'auto' ? this.detectLanguage(text) : sourceLang;
    
    // 模拟翻译
    const translationMap: any = {
      'zh-cn': {
        'en': 'This is a translated text from Chinese to English.',
        'ja': 'これは中国語から日本語に翻訳されたテキストです。',
        'ko': '이것은 중국어에서 한국어로 번역된 텍스트입니다.'
      },
      'en': {
        'zh-cn': '这是从英语翻译成中文的文本。',
        'ja': 'これは英語から日本語に翻訳されたテキストです。',
        'ko': '이것은 영어에서 한국어로 번역된 텍스트입니다.'
      },
      'ja': {
        'en': 'This is a translated text from Japanese to English.',
        'zh-cn': '这是从日语翻译成中文的文本。',
        'ko': '이것은 일본어에서 한국어로 번역된 텍스트입니다.'
      }
    };

    const translatedText = translationMap[detectedLanguage]?.[targetLang] || 
                          `Translated text from ${detectedLanguage} to ${targetLang}: ${text}`;

    const confidence = Math.random() * 0.3 + 0.7; // 70-100%

    // 生成备选翻译
    const alternatives = generateAlternatives ? [
      { text: `Alternative translation 1: ${translatedText}`, confidence: confidence - 0.1 },
      { text: `Alternative translation 2: ${translatedText}`, confidence: confidence - 0.2 }
    ] : undefined;

    return {
      translatedText,
      sourceLanguage: detectedLanguage,
      targetLanguage: targetLang,
      confidence,
      alternatives
    };
  }

  private detectLanguage(text: string): string {
    // 简单的语言检测模拟
    if (/[\u4e00-\u9fff]/.test(text)) return 'zh-cn';
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return 'ja';
    if (/[\uac00-\ud7af]/.test(text)) return 'ko';
    return 'en';
  }
}

/**
 * 问答系统节点
 * 基于上下文回答问题
 */
export class QuestionAnsweringNode extends VisualScriptNode {
  constructor() {
    super('QuestionAnswering', '问答系统');
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('question', 'string', '问题', '');
    this.addInput('context', 'string', '上下文', '');
    this.addInput('modelId', 'string', 'QA模型ID', '');
    this.addInput('maxAnswerLength', 'number', '最大答案长度', 100);
    this.addInput('confidenceThreshold', 'number', '置信度阈值', 0.5);
    this.addInput('returnMultiple', 'boolean', '返回多个答案', false);
  }

  private setupOutputs(): void {
    this.addOutput('answer', 'string', '答案');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('startIndex', 'number', '开始位置');
    this.addOutput('endIndex', 'number', '结束位置');
    this.addOutput('supportingEvidence', 'array', '支持证据');
    this.addOutput('onAnswered', 'boolean', '回答完成');
    this.addOutput('onNoAnswer', 'boolean', '无法回答');
  }

  public execute(inputs?: any): any {
    try {
      const question = inputs?.question || this.getInputValue('question');
      const context = inputs?.context || this.getInputValue('context');
      const modelId = inputs?.modelId || this.getInputValue('modelId');
      const maxAnswerLength = inputs?.maxAnswerLength || this.getInputValue('maxAnswerLength');
      const confidenceThreshold = inputs?.confidenceThreshold || this.getInputValue('confidenceThreshold');
      const returnMultiple = inputs?.returnMultiple !== undefined ? inputs.returnMultiple : this.getInputValue('returnMultiple');

      if (!question) {
        throw new Error('问题不能为空');
      }

      if (!context) {
        throw new Error('上下文不能为空');
      }

      Debug.log('QuestionAnsweringNode', `开始问答: ${question}`);

      const result = this.answerQuestion(question, context, modelId, maxAnswerLength, confidenceThreshold, returnMultiple);

      Debug.log('QuestionAnsweringNode', `问答完成，置信度: ${result.confidence}`);

      return {
        answer: result.answer,
        confidence: result.confidence,
        startIndex: result.startIndex,
        endIndex: result.endIndex,
        supportingEvidence: result.supportingEvidence || [],
        onAnswered: result.confidence >= confidenceThreshold,
        onNoAnswer: result.confidence < confidenceThreshold
      };

    } catch (error) {
      Debug.error('QuestionAnsweringNode', '问答失败:', error);
      return {
        answer: '',
        confidence: 0,
        startIndex: -1,
        endIndex: -1,
        supportingEvidence: [],
        onAnswered: false,
        onNoAnswer: true
      };
    }
  }

  private answerQuestion(question: string, context: string, modelId: string, maxLength: number, threshold: number, returnMultiple: boolean): QuestionAnsweringResult {
    // 模拟问答过程
    const words = context.split(/\s+/);
    const startIndex = Math.floor(Math.random() * Math.max(1, words.length - 5));
    const answerLength = Math.min(maxLength, Math.floor(Math.random() * 10) + 3);
    const endIndex = Math.min(startIndex + answerLength, words.length);
    
    const answer = words.slice(startIndex, endIndex).join(' ');
    const confidence = Math.random() * 0.5 + 0.5; // 50-100%

    // 生成支持证据
    const supportingEvidence = [
      `Evidence 1: ${words.slice(Math.max(0, startIndex - 3), startIndex).join(' ')} ${answer}`,
      `Evidence 2: ${answer} ${words.slice(endIndex, Math.min(endIndex + 3, words.length)).join(' ')}`
    ];

    return {
      answer,
      confidence,
      startIndex: context.indexOf(answer),
      endIndex: context.indexOf(answer) + answer.length,
      context,
      supportingEvidence
    };
  }
}

/**
 * 文本生成节点
 * 基于提示生成文本内容
 */
export class TextGenerationNode extends VisualScriptNode {
  constructor() {
    super('TextGeneration', '文本生成');
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('prompt', 'string', '生成提示', '');
    this.addInput('modelId', 'string', '生成模型ID', '');
    this.addInput('maxTokens', 'number', '最大令牌数', 100);
    this.addInput('temperature', 'number', '温度', 0.7);
    this.addInput('topP', 'number', 'Top-p', 0.9);
    this.addInput('stopSequences', 'array', '停止序列', []);
    this.addInput('generateMultiple', 'boolean', '生成多个', false);
  }

  private setupOutputs(): void {
    this.addOutput('generatedText', 'string', '生成文本');
    this.addOutput('completionTokens', 'number', '完成令牌数');
    this.addOutput('totalTokens', 'number', '总令牌数');
    this.addOutput('finishReason', 'string', '完成原因');
    this.addOutput('alternatives', 'array', '备选生成');
    this.addOutput('onGenerated', 'boolean', '生成完成');
    this.addOutput('onError', 'boolean', '生成失败');
  }

  public execute(inputs?: any): any {
    try {
      const prompt = inputs?.prompt || this.getInputValue('prompt');
      const modelId = inputs?.modelId || this.getInputValue('modelId');
      const maxTokens = inputs?.maxTokens || this.getInputValue('maxTokens');
      const temperature = inputs?.temperature || this.getInputValue('temperature');
      const topP = inputs?.topP || this.getInputValue('topP');
      const stopSequences = inputs?.stopSequences || this.getInputValue('stopSequences');
      const generateMultiple = inputs?.generateMultiple !== undefined ? inputs.generateMultiple : this.getInputValue('generateMultiple');

      if (!prompt) {
        throw new Error('生成提示不能为空');
      }

      Debug.log('TextGenerationNode', `开始文本生成: ${prompt.substring(0, 50)}...`);

      const result = this.generateText(prompt, modelId, maxTokens, temperature, topP, stopSequences, generateMultiple);

      Debug.log('TextGenerationNode', `文本生成完成，生成 ${result.completionTokens} 个令牌`);

      return {
        generatedText: result.generatedText,
        completionTokens: result.completionTokens,
        totalTokens: result.totalTokens,
        finishReason: result.finishReason,
        alternatives: result.alternatives || [],
        onGenerated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('TextGenerationNode', '文本生成失败:', error);
      return {
        generatedText: '',
        completionTokens: 0,
        totalTokens: 0,
        finishReason: 'error',
        alternatives: [],
        onGenerated: false,
        onError: true
      };
    }
  }

  private generateText(prompt: string, modelId: string, maxTokens: number, temperature: number, topP: number, stopSequences: string[], generateMultiple: boolean): TextGenerationResult {
    // 模拟文本生成
    const promptTokens = Math.floor(prompt.length / 4); // 粗略估算
    const completionTokens = Math.min(maxTokens, Math.floor(Math.random() * maxTokens * 0.8) + 20);
    
    // 根据温度调整生成的随机性
    const creativity = Math.min(1, temperature);
    const generatedText = this.simulateGeneration(prompt, completionTokens, creativity);
    
    // 检查停止序列
    let finalText = generatedText;
    let finishReason: 'stop' | 'length' | 'content_filter' = 'length';
    
    for (const stopSeq of stopSequences) {
      const stopIndex = generatedText.indexOf(stopSeq);
      if (stopIndex !== -1) {
        finalText = generatedText.substring(0, stopIndex);
        finishReason = 'stop';
        break;
      }
    }

    // 检查内容过滤
    if (this.containsInappropriateContent(finalText)) {
      finishReason = 'content_filter';
      finalText = '[Content filtered]';
    }

    // 生成备选文本
    const alternatives = generateMultiple ? [
      `Alternative generation 1: ${this.simulateGeneration(prompt, completionTokens, creativity)}`,
      `Alternative generation 2: ${this.simulateGeneration(prompt, completionTokens, creativity)}`
    ] : undefined;

    return {
      generatedText: finalText,
      prompt,
      completionTokens: Math.floor(finalText.length / 4),
      totalTokens: promptTokens + Math.floor(finalText.length / 4),
      finishReason,
      alternatives
    };
  }

  private simulateGeneration(prompt: string, tokens: number, creativity: number): string {
    // 简单的文本生成模拟
    const continuations = [
      'This is a continuation of the prompt. It provides relevant and coherent information.',
      'The generated text follows naturally from the input prompt and maintains context.',
      'Here is some generated content that responds to the given prompt appropriately.',
      'This text generation demonstrates the capability to produce meaningful content.',
      'The AI model has generated this text based on the provided prompt and parameters.'
    ];

    const selectedContinuation = continuations[Math.floor(Math.random() * continuations.length)];
    
    // 根据创造性调整文本
    if (creativity > 0.8) {
      return `${selectedContinuation} With high creativity, this text explores novel ideas and unique perspectives.`;
    } else if (creativity < 0.3) {
      return `${selectedContinuation} This is a conservative and predictable continuation.`;
    } else {
      return selectedContinuation;
    }
  }

  private containsInappropriateContent(text: string): boolean {
    // 简单的内容过滤模拟
    const inappropriateWords = ['inappropriate', 'harmful', 'offensive'];
    return inappropriateWords.some(word => text.toLowerCase().includes(word));
  }
}

// 导出所有自然语言处理节点
export const NLP_NODES_BATCH33 = [
  MachineTranslationNode,
  QuestionAnsweringNode,
  TextGenerationNode
];

// 导出节点类型映射
export const NLP_NODE_TYPES_BATCH33 = {
  'MachineTranslation': MachineTranslationNode,
  'QuestionAnswering': QuestionAnsweringNode,
  'TextGeneration': TextGenerationNode
} as const;
