# 批次3.3节点API文档

本文档详细介绍了DL引擎视觉脚本系统批次3.3新增的25个节点的API接口和使用方法。

## 概述

批次3.3包含以下三类节点：
- **AI工具节点**（10个）：模型部署、监控、版本控制等高级AI工具
- **计算机视觉节点**（8个）：图像分割、目标跟踪、人脸识别等视觉处理
- **自然语言处理节点**（7个）：文本分类、机器翻译、文本生成等NLP功能

## AI工具节点

### 1. ModelDeploymentNode - 模型部署节点

**功能描述**：将训练好的模型部署到指定环境

**输入参数**：
- `modelId` (string): 模型ID
- `deploymentConfig` (object): 部署配置
- `environment` (string): 目标环境 (production/staging/development)
- `autoScale` (boolean): 是否启用自动扩缩容

**输出结果**：
- `deploymentId` (string): 部署ID
- `endpoint` (string): 服务端点URL
- `status` (string): 部署状态
- `metrics` (object): 部署指标
- `onDeployed` (boolean): 部署完成事件
- `onError` (boolean): 部署失败事件

**使用示例**：
```javascript
const deploymentNode = new ModelDeploymentNode();
const result = deploymentNode.execute({
  modelId: 'my-model-v1.0',
  deploymentConfig: {
    cpu: '2000m',
    memory: '4Gi',
    gpu: '1'
  },
  environment: 'production',
  autoScale: true
});
```

### 2. AutoMLNode - 自动机器学习节点

**功能描述**：自动化机器学习流程，包括特征工程、模型选择和超参数优化

**输入参数**：
- `datasetPath` (string): 数据集路径
- `taskType` (string): 任务类型 (classification/regression/clustering)
- `targetColumn` (string): 目标列名
- `timeLimit` (number): 时间限制（分钟）
- `config` (object): AutoML配置

**输出结果**：
- `bestModel` (object): 最佳模型信息
- `leaderboard` (array): 模型排行榜
- `featureImportance` (array): 特征重要性
- `modelMetrics` (object): 模型指标
- `onCompleted` (boolean): 训练完成事件

### 3. ExplainableAINode - 可解释AI节点

**功能描述**：提供模型解释和可解释性分析

**输入参数**：
- `modelId` (string): 模型ID
- `inputData` (object): 输入数据
- `explanationType` (string): 解释类型 (global/local/both)
- `visualize` (boolean): 是否生成可视化

**输出结果**：
- `explanation` (object): 解释结果
- `featureImportance` (array): 特征重要性
- `shapValues` (array): SHAP值
- `visualizations` (object): 可视化结果

### 4. ModelCompressionNode - 模型压缩节点

**功能描述**：压缩模型以减少大小和提高推理速度

**输入参数**：
- `modelId` (string): 模型ID
- `compressionMethod` (string): 压缩方法 (pruning/quantization/distillation)
- `targetRatio` (number): 目标压缩比
- `qualityThreshold` (number): 质量阈值

**输出结果**：
- `compressedModelId` (string): 压缩后模型ID
- `compressionRatio` (number): 实际压缩比
- `qualityMetrics` (object): 质量指标
- `performanceGain` (object): 性能提升

## 计算机视觉节点

### 1. ImageSegmentationNode - 图像分割节点

**功能描述**：对图像进行语义分割或实例分割

**输入参数**：
- `image` (object): 输入图像
- `modelId` (string): 分割模型ID
- `segmentationType` (string): 分割类型 (semantic/instance/panoptic)
- `confidenceThreshold` (number): 置信度阈值
- `outputMask` (boolean): 是否输出掩码

**输出结果**：
- `segments` (array): 分割结果
- `segmentedImage` (object): 分割后图像
- `masks` (array): 分割掩码
- `segmentCount` (number): 分割区域数量

### 2. FaceRecognitionNode - 人脸识别节点

**功能描述**：检测和识别图像中的人脸

**输入参数**：
- `image` (object): 输入图像
- `faceDatabase` (object): 人脸数据库
- `detectionThreshold` (number): 检测阈值
- `recognitionThreshold` (number): 识别阈值
- `extractAttributes` (boolean): 是否提取属性

**输出结果**：
- `faces` (array): 人脸检测结果
- `faceCount` (number): 人脸数量
- `recognizedFaces` (array): 已识别人脸
- `unknownFaces` (array): 未知人脸

### 3. ImageGenerationNode - 图像生成节点

**功能描述**：使用AI模型生成图像

**输入参数**：
- `prompt` (string): 生成提示
- `negativePrompt` (string): 负面提示
- `width` (number): 图像宽度
- `height` (number): 图像高度
- `steps` (number): 生成步数
- `guidanceScale` (number): 引导强度

**输出结果**：
- `generatedImage` (object): 生成的图像
- `imageUrl` (string): 图像URL
- `metadata` (object): 生成元数据
- `generationTime` (number): 生成时间

### 4. AugmentedRealityNode - 增强现实节点

**功能描述**：在现实场景中叠加虚拟内容

**输入参数**：
- `cameraImage` (object): 相机图像
- `virtualObjects` (array): 虚拟对象
- `trackingMarkers` (array): 跟踪标记
- `cameraMatrix` (object): 相机矩阵
- `enableOcclusion` (boolean): 启用遮挡
- `lightingEstimation` (boolean): 光照估计

**输出结果**：
- `arImage` (object): AR合成图像
- `trackedMarkers` (array): 跟踪到的标记
- `virtualObjectPoses` (array): 虚拟对象姿态
- `lightingInfo` (object): 光照信息

## 自然语言处理节点

### 1. TextClassificationNode - 文本分类节点

**功能描述**：对文本进行分类和标签预测

**输入参数**：
- `text` (string): 输入文本
- `modelId` (string): 分类模型ID
- `classificationTask` (string): 分类任务 (sentiment/topic/intent/spam)
- `topK` (number): 返回前K个结果
- `confidenceThreshold` (number): 置信度阈值

**输出结果**：
- `classifications` (array): 分类结果
- `topClassification` (object): 最佳分类
- `confidence` (number): 最高置信度
- `allScores` (object): 所有分数

### 2. MachineTranslationNode - 机器翻译节点

**功能描述**：将文本从一种语言翻译为另一种语言

**输入参数**：
- `text` (string): 输入文本
- `sourceLanguage` (string): 源语言
- `targetLanguage` (string): 目标语言
- `domain` (string): 领域 (general/medical/legal/technical)
- `generateAlternatives` (boolean): 生成备选翻译

**输出结果**：
- `translatedText` (string): 翻译文本
- `detectedLanguage` (string): 检测到的语言
- `confidence` (number): 翻译置信度
- `alternatives` (array): 备选翻译

### 3. TextGenerationNode - 文本生成节点

**功能描述**：基于提示生成文本内容

**输入参数**：
- `prompt` (string): 生成提示
- `modelId` (string): 生成模型ID
- `maxTokens` (number): 最大令牌数
- `temperature` (number): 温度参数
- `topP` (number): Top-p参数
- `stopSequences` (array): 停止序列

**输出结果**：
- `generatedText` (string): 生成的文本
- `completionTokens` (number): 完成令牌数
- `totalTokens` (number): 总令牌数
- `finishReason` (string): 完成原因

### 4. QuestionAnsweringNode - 问答系统节点

**功能描述**：基于上下文回答问题

**输入参数**：
- `question` (string): 问题
- `context` (string): 上下文
- `modelId` (string): QA模型ID
- `maxAnswerLength` (number): 最大答案长度
- `confidenceThreshold` (number): 置信度阈值

**输出结果**：
- `answer` (string): 答案
- `confidence` (number): 置信度
- `startIndex` (number): 答案开始位置
- `endIndex` (number): 答案结束位置
- `supportingEvidence` (array): 支持证据

## 使用注意事项

1. **错误处理**：所有节点都提供`onError`输出，用于处理执行失败的情况
2. **异步执行**：某些节点（如模型训练、图像生成）可能需要较长时间执行
3. **资源管理**：AI模型节点可能消耗大量计算资源，请合理配置
4. **数据格式**：确保输入数据格式符合节点要求
5. **模型依赖**：某些节点需要预训练模型，请确保模型可用

## 集成示例

```javascript
// 创建一个完整的AI工作流
const workflow = new VisualScriptWorkflow();

// 1. 文本分类
const classifier = new TextClassificationNode();
workflow.addNode(classifier);

// 2. 基于分类结果进行不同处理
const translator = new MachineTranslationNode();
const generator = new TextGenerationNode();

workflow.addNode(translator);
workflow.addNode(generator);

// 3. 连接节点
workflow.connect(classifier, 'topClassification', translator, 'text');
workflow.connect(classifier, 'confidence', generator, 'temperature');

// 4. 执行工作流
const result = await workflow.execute({
  text: 'Hello, how are you today?'
});
```

## 更新日志

- **v3.3.0** (2024-12-19): 新增25个AI节点，包括AI工具、计算机视觉和自然语言处理功能
- 完善了节点的错误处理和事件机制
- 增加了详细的API文档和使用示例
- 提供了完整的单元测试覆盖
