/**
 * 批次3.3节点测试
 * 深度学习和机器学习节点功能验证
 */

import {
  DeepLearningModelNode,
  NeuralNetworkNode,
  ConvolutionalNetworkNode,
  RecurrentNetworkNode,
  TransformerModelNode,
  GANModelNode,
  VAEModelNode,
  AttentionMechanismNode,
  EmbeddingLayerNode,
  DropoutLayerNode,
  BatchNormalizationNode,
  ActivationFunctionNode,
  LossFunctionNode,
  OptimizerNode,
  RegularizationNode
} from '../DeepLearningNodes';

import {
  ReinforcementLearningNode,
  FederatedLearningNode
} from '../MachineLearningNodes';

import {
  TransferLearningNode,
  ModelEnsembleNode
} from '../MachineLearningNodes2';

import {
  HyperparameterTuningNode,
  ModelValidationNode
} from '../MachineLearningNodes3';

import {
  CrossValidationNode,
  FeatureSelectionNode,
  DimensionalityReductionNode,
  ClusteringNode
} from '../MachineLearningNodes4';

import { batch33NodesRegistry } from '../../registry/Batch33NodesRegistry';

// 新增批次3.3节点导入
// AI工具节点
import {
  ModelDeploymentNode,
  ModelMonitoringNode,
  ModelVersioningNode
} from '../AIToolNodes';

import {
  AutoMLNode,
  ExplainableAINode,
  AIEthicsNode,
  ModelCompressionNode,
  QuantizationNode
} from '../AIToolNodes2';

import {
  PruningNode,
  DistillationNode
} from '../AIToolNodes3';

// 计算机视觉节点
import {
  ImageSegmentationNode,
  ObjectTrackingNode,
  FaceRecognitionNode,
  OpticalCharacterRecognitionNode
} from '../ComputerVisionNodes2';

import {
  ImageGenerationNode,
  StyleTransferNode,
  ImageEnhancementNode,
  AugmentedRealityNode
} from '../ComputerVisionNodes3';

// 自然语言处理节点
import {
  TextClassificationNode,
  NamedEntityRecognitionNode,
  SentimentAnalysisNode as SentimentAnalysisNodeNew,
  TextSummarizationNode
} from '../NaturalLanguageProcessingNodes';

import {
  MachineTranslationNode,
  QuestionAnsweringNode,
  TextGenerationNode
} from '../NaturalLanguageProcessingNodes2';

describe('批次3.3节点测试', () => {
  describe('深度学习节点测试', () => {
    test('深度学习模型节点', () => {
      const node = new DeepLearningModelNode();
      
      const inputs = {
        modelId: 'test-model',
        modelType: 'feedforward',
        inputSize: 784,
        outputSize: 10,
        hiddenLayers: [128, 64],
        activation: 'relu'
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.model).toBeDefined();
      expect(result.modelInfo).toBeDefined();
      expect(result.modelInfo.id).toBe('test-model');
      expect(result.modelInfo.type).toBe('feedforward');
    });

    test('神经网络节点', () => {
      const node = new NeuralNetworkNode();
      
      const inputs = {
        inputData: [1, 2, 3, 4],
        weights: [[[0.1, 0.2], [0.3, 0.4], [0.5, 0.6], [0.7, 0.8]]],
        biases: [[0.1, 0.2]],
        activationFunction: 'sigmoid'
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.output).toBeDefined();
      expect(Array.isArray(result.output)).toBe(true);
      expect(result.activations).toBeDefined();
    });

    test('卷积神经网络节点', () => {
      const node = new ConvolutionalNetworkNode();
      
      const inputs = {
        inputImage: [[1, 2, 3], [4, 5, 6], [7, 8, 9]],
        filters: [[[1, 0], [0, 1]]],
        kernelSize: 2,
        stride: 1,
        padding: 'same',
        poolingType: 'max',
        poolingSize: 2
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.featureMaps).toBeDefined();
      expect(result.pooledMaps).toBeDefined();
      expect(result.outputShape).toBeDefined();
    });

    test('循环神经网络节点', () => {
      const node = new RecurrentNetworkNode();
      
      const inputs = {
        sequenceData: [[1, 2], [3, 4], [5, 6]],
        hiddenSize: 4,
        sequenceLength: 3,
        returnSequences: true,
        weights: {}
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.hiddenStates).toBeDefined();
      expect(result.outputs).toBeDefined();
      expect(result.finalOutput).toBeDefined();
    });

    test('Transformer模型节点', () => {
      const node = new TransformerModelNode();
      
      const inputs = {
        inputSequence: [[1, 2, 3], [4, 5, 6]],
        numHeads: 2,
        dModel: 6,
        dff: 12,
        numLayers: 2,
        maxLength: 10
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.encodedSequence).toBeDefined();
      expect(result.attentionWeights).toBeDefined();
      expect(result.positionEncoding).toBeDefined();
    });

    test('生成对抗网络节点', () => {
      const node = new GANModelNode();
      
      const inputs = {
        noiseVector: [0.1, 0.2, 0.3, 0.4],
        realData: [0.8, 0.9, 0.7, 0.6],
        generatorLayers: [4, 8, 4],
        discriminatorLayers: [4, 2, 1],
        learningRate: 0.0002
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.generatedData).toBeDefined();
      expect(result.discriminatorScore).toBeDefined();
      expect(result.generatorLoss).toBeDefined();
      expect(result.discriminatorLoss).toBeDefined();
    });

    test('变分自编码器节点', () => {
      const node = new VAEModelNode();
      
      const inputs = {
        inputData: [1, 2, 3, 4, 5],
        latentDim: 2,
        encoderLayers: [5, 4, 4],
        decoderLayers: [2, 4, 5]
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.reconstructed).toBeDefined();
      expect(result.latentMean).toBeDefined();
      expect(result.latentLogVar).toBeDefined();
      expect(result.klLoss).toBeDefined();
      expect(result.reconstructionLoss).toBeDefined();
    });

    test('注意力机制节点', () => {
      const node = new AttentionMechanismNode();
      
      const inputs = {
        query: [1, 2, 3],
        key: [[1, 2, 3], [4, 5, 6]],
        value: [[0.1, 0.2], [0.3, 0.4]],
        attentionType: 'scaled_dot_product',
        temperature: 1.0
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.attentionOutput).toBeDefined();
      expect(result.attentionWeights).toBeDefined();
      expect(result.alignmentScores).toBeDefined();
    });

    test('嵌入层节点', () => {
      const node = new EmbeddingLayerNode();
      
      const inputs = {
        inputIndices: [1, 2, 3, 0],
        vocabSize: 100,
        embeddingDim: 50,
        paddingIdx: 0
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.embeddings).toBeDefined();
      expect(result.embeddingMatrix).toBeDefined();
      expect(result.embeddings.length).toBe(4);
      expect(result.embeddings[0].length).toBe(50);
    });

    test('Dropout层节点', () => {
      const node = new DropoutLayerNode();
      
      const inputs = {
        inputData: [1, 2, 3, 4, 5],
        dropoutRate: 0.5,
        training: true
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.outputData).toBeDefined();
      expect(result.mask).toBeDefined();
      expect(result.outputData.length).toBe(5);
      expect(result.mask.length).toBe(5);
    });

    test('批量归一化节点', () => {
      const node = new BatchNormalizationNode();
      
      const inputs = {
        inputData: [1, 2, 3, 4, 5],
        gamma: [1, 1, 1, 1, 1],
        beta: [0, 0, 0, 0, 0],
        epsilon: 1e-5,
        momentum: 0.9
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.normalizedData).toBeDefined();
      expect(result.mean).toBeDefined();
      expect(result.variance).toBeDefined();
    });

    test('激活函数节点', () => {
      const node = new ActivationFunctionNode();
      
      const inputs = {
        inputData: [-2, -1, 0, 1, 2],
        activationType: 'relu',
        alpha: 0.01
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.activatedData).toBeDefined();
      expect(result.derivative).toBeDefined();
      expect(result.activatedData[0]).toBe(0); // ReLU(-2) = 0
      expect(result.activatedData[4]).toBe(2); // ReLU(2) = 2
    });

    test('损失函数节点', () => {
      const node = new LossFunctionNode();
      
      const inputs = {
        predictions: [0.8, 0.3, 0.9, 0.2],
        targets: [1, 0, 1, 0],
        lossType: 'mse',
        reduction: 'mean'
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.loss).toBeDefined();
      expect(result.gradient).toBeDefined();
      expect(result.itemLosses).toBeDefined();
      expect(typeof result.loss).toBe('number');
    });

    test('优化器节点', () => {
      const node = new OptimizerNode();
      
      const inputs = {
        parameters: [1.0, 2.0, 3.0],
        gradients: [0.1, 0.2, 0.3],
        optimizerType: 'adam',
        learningRate: 0.001,
        momentum: 0.9,
        beta1: 0.9,
        beta2: 0.999,
        epsilon: 1e-8
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.updatedParameters).toBeDefined();
      expect(result.stepSize).toBeDefined();
      expect(result.gradientNorm).toBeDefined();
      expect(result.updatedParameters.length).toBe(3);
    });

    test('正则化节点', () => {
      const node = new RegularizationNode();
      
      const inputs = {
        parameters: [1.0, -2.0, 3.0],
        regularizationType: 'l2',
        lambda: 0.01,
        p: 2
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.regularizationLoss).toBeDefined();
      expect(result.regularizationGradient).toBeDefined();
      expect(result.parameterNorm).toBeDefined();
      expect(typeof result.regularizationLoss).toBe('number');
    });
  });

  describe('机器学习节点测试', () => {
    test('强化学习节点', () => {
      const node = new ReinforcementLearningNode();
      
      const inputs = {
        state: 'state1',
        action: 'action1',
        reward: 1.0,
        nextState: 'state2',
        availableActions: ['action1', 'action2', 'action3'],
        learningRate: 0.1,
        discountFactor: 0.9,
        explorationRate: 0.1
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.bestAction).toBeDefined();
      expect(result.qValue).toBeDefined();
      expect(result.qTable).toBeDefined();
      expect(typeof result.explorationAction).toBe('boolean');
    });

    test('联邦学习节点', () => {
      const node = new FederatedLearningNode();
      
      const inputs = {
        clientId: 'client1',
        localModel: { parameters: [1, 2, 3] },
        aggregationMethod: 'fedavg',
        clientWeight: 1.0,
        minClients: 2
      };

      const result = node.execute(inputs);
      
      expect(result.success).toBe(true);
      expect(result.globalModel).toBeDefined();
      expect(result.aggregationRound).toBeDefined();
      expect(result.participatingClients).toBeDefined();
      expect(result.convergenceMetric).toBeDefined();
    });
  });

  describe('节点注册表测试', () => {
    test('批次3.3节点注册表', () => {
      const registry = batch33NodesRegistry;
      
      // 测试节点类型获取
      const nodeTypes = registry.getAllRegisteredNodeTypes();
      expect(nodeTypes.length).toBe(25);
      
      // 测试深度学习节点
      expect(nodeTypes).toContain('ai/deepLearningModel');
      expect(nodeTypes).toContain('ai/neuralNetwork');
      expect(nodeTypes).toContain('ai/convolutionalNetwork');
      
      // 测试机器学习节点
      expect(nodeTypes).toContain('ml/reinforcementLearning');
      expect(nodeTypes).toContain('ml/federatedLearning');
      expect(nodeTypes).toContain('ml/transferLearning');
      
      // 测试节点创建
      const dlModel = registry.createNode('ai/deepLearningModel');
      expect(dlModel).toBeInstanceOf(DeepLearningModelNode);
      
      const rlNode = registry.createNode('ml/reinforcementLearning');
      expect(rlNode).toBeInstanceOf(ReinforcementLearningNode);
      
      // 测试节点统计
      const stats = registry.getNodeCategoryStats();
      expect(stats['深度学习']).toBe(15);
      expect(stats['机器学习']).toBe(10);
      expect(stats['总计']).toBe(25);
    });
  });

  describe('新增批次3.3节点测试 - AI工具节点', () => {
    test('ModelDeploymentNode - 模型部署', () => {
      const node = new ModelDeploymentNode();
      expect(node.nodeType).toBe('ModelDeployment');
      expect(node.name).toBe('模型部署');

      const result = node.execute({
        modelId: 'test-model-001',
        deploymentConfig: { cpu: '2000m', memory: '4Gi' },
        environment: 'production',
        autoScale: true
      });

      expect(result.deploymentId).toBeDefined();
      expect(result.endpoint).toContain('production');
      expect(result.status).toBe('deployed');
      expect(result.onDeployed).toBe(true);
    });

    test('AutoMLNode - 自动机器学习', () => {
      const node = new AutoMLNode();
      expect(node.nodeType).toBe('AutoML');

      const result = node.execute({
        datasetPath: '/data/train.csv',
        taskType: 'classification',
        targetColumn: 'label',
        timeLimit: 60
      });

      expect(result.bestModel).toBeDefined();
      expect(result.leaderboard).toBeInstanceOf(Array);
      expect(result.onCompleted).toBe(true);
    });

    test('ExplainableAINode - 可解释AI', () => {
      const node = new ExplainableAINode();
      expect(node.nodeType).toBe('ExplainableAI');

      const result = node.execute({
        modelId: 'test-model',
        inputData: { age: 35, income: 50000 },
        explanationType: 'both'
      });

      expect(result.explanation).toBeDefined();
      expect(result.featureImportance).toBeInstanceOf(Array);
      expect(result.onExplained).toBe(true);
    });

    test('ModelCompressionNode - 模型压缩', () => {
      const node = new ModelCompressionNode();
      expect(node.nodeType).toBe('ModelCompression');

      const result = node.execute({
        modelId: 'test-model',
        compressionMethod: 'quantization',
        targetRatio: 0.5
      });

      expect(result.compressedModelId).toBeDefined();
      expect(result.compressionRatio).toBeGreaterThan(0);
      expect(result.onCompleted).toBe(true);
    });
  });

  describe('新增批次3.3节点测试 - 计算机视觉节点', () => {
    test('ImageSegmentationNode - 图像分割', () => {
      const node = new ImageSegmentationNode();
      expect(node.nodeType).toBe('ImageSegmentation');

      const result = node.execute({
        image: { width: 640, height: 480, data: 'mock_image_data' },
        segmentationType: 'semantic',
        confidenceThreshold: 0.5
      });

      expect(result.segments).toBeInstanceOf(Array);
      expect(result.segmentCount).toBeGreaterThanOrEqual(0);
      expect(result.onSegmented).toBe(true);
    });

    test('FaceRecognitionNode - 人脸识别', () => {
      const node = new FaceRecognitionNode();
      expect(node.nodeType).toBe('FaceRecognition');

      const result = node.execute({
        image: { width: 640, height: 480, data: 'mock_image_data' },
        detectionThreshold: 0.7
      });

      expect(result.faces).toBeInstanceOf(Array);
      expect(result.faceCount).toBeGreaterThanOrEqual(0);
    });

    test('ImageGenerationNode - 图像生成', () => {
      const node = new ImageGenerationNode();
      expect(node.nodeType).toBe('ImageGeneration');

      const result = node.execute({
        prompt: 'A beautiful landscape',
        width: 512,
        height: 512
      });

      expect(result.generatedImage).toBeDefined();
      expect(result.imageUrl).toBeDefined();
      expect(result.onGenerated).toBe(true);
    });

    test('AugmentedRealityNode - 增强现实', () => {
      const node = new AugmentedRealityNode();
      expect(node.nodeType).toBe('AugmentedReality');

      const result = node.execute({
        cameraImage: { width: 640, height: 480 },
        virtualObjects: [],
        trackingMarkers: []
      });

      expect(result.arImage).toBeDefined();
      expect(result.trackedMarkers).toBeInstanceOf(Array);
    });
  });

  describe('新增批次3.3节点测试 - 自然语言处理节点', () => {
    test('TextClassificationNode - 文本分类', () => {
      const node = new TextClassificationNode();
      expect(node.nodeType).toBe('TextClassification');

      const result = node.execute({
        text: 'This is a great product!',
        classificationTask: 'sentiment',
        topK: 3
      });

      expect(result.classifications).toBeInstanceOf(Array);
      expect(result.topClassification).toBeDefined();
      expect(result.onClassified).toBe(true);
    });

    test('MachineTranslationNode - 机器翻译', () => {
      const node = new MachineTranslationNode();
      expect(node.nodeType).toBe('MachineTranslation');

      const result = node.execute({
        text: 'Hello, world!',
        sourceLanguage: 'en',
        targetLanguage: 'zh-cn'
      });

      expect(result.translatedText).toBeDefined();
      expect(result.detectedLanguage).toBeDefined();
      expect(result.onTranslated).toBe(true);
    });

    test('TextGenerationNode - 文本生成', () => {
      const node = new TextGenerationNode();
      expect(node.nodeType).toBe('TextGeneration');

      const result = node.execute({
        prompt: 'Once upon a time',
        maxTokens: 100
      });

      expect(result.generatedText).toBeDefined();
      expect(result.completionTokens).toBeGreaterThan(0);
      expect(result.onGenerated).toBe(true);
    });

    test('QuestionAnsweringNode - 问答系统', () => {
      const node = new QuestionAnsweringNode();
      expect(node.nodeType).toBe('QuestionAnswering');

      const result = node.execute({
        question: 'What is AI?',
        context: 'Artificial Intelligence (AI) is a branch of computer science.'
      });

      expect(result.answer).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
    });
  });
});
