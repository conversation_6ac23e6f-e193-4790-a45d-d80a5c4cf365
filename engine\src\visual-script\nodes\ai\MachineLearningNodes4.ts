/**
 * 机器学习节点 - 第四部分
 * 完成批次3.3的机器学习节点（7-10）
 */

import { VisualScriptNode } from '../../VisualScriptNode';
import { MachineLearningNode } from './MachineLearningNodes';

/**
 * 7. 交叉验证节点
 */
export class CrossValidationNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/crossValidation';
  public static readonly NAME = '交叉验证';
  public static readonly DESCRIPTION = 'K折交叉验证';

  constructor() {
    super(CrossValidationNode.TYPE, CrossValidationNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('dataset', 'array', '数据集', []);
    this.addInput('labels', 'array', '标签', []);
    this.addInput('kFolds', 'number', 'K折数', 5);
    this.addInput('modelConfig', 'object', '模型配置', {});
    this.addInput('stratified', 'boolean', '分层采样', true);
    this.addInput('shuffle', 'boolean', '随机打乱', true);
  }

  private setupOutputs(): void {
    this.addOutput('cvScores', 'array', '交叉验证得分');
    this.addOutput('meanScore', 'number', '平均得分');
    this.addOutput('stdScore', 'number', '得分标准差');
    this.addOutput('foldResults', 'array', '各折结果');
  }

  public execute(inputs: any): any {
    try {
      const dataset = this.getInputValue(inputs, 'dataset');
      const labels = this.getInputValue(inputs, 'labels');
      const kFolds = this.getInputValue(inputs, 'kFolds');
      const modelConfig = this.getInputValue(inputs, 'modelConfig');
      const stratified = this.getInputValue(inputs, 'stratified');
      const shuffle = this.getInputValue(inputs, 'shuffle');

      if (!Array.isArray(dataset) || !Array.isArray(labels)) {
        throw new Error('数据集或标签无效');
      }

      if (dataset.length !== labels.length) {
        throw new Error('数据集和标签长度不匹配');
      }

      // 执行K折交叉验证
      const cvResult = this.performCrossValidation(
        dataset, labels, kFolds, modelConfig, stratified, shuffle
      );

      return {
        cvScores: cvResult.scores,
        meanScore: cvResult.meanScore,
        stdScore: cvResult.stdScore,
        foldResults: cvResult.foldResults,
        result: {
          status: 'completed',
          kFolds,
          meanScore: cvResult.meanScore,
          confidence: cvResult.confidence
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        cvScores: [],
        meanScore: 0,
        stdScore: 0,
        foldResults: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '交叉验证失败'
      };
    }
  }

  private performCrossValidation(
    dataset: any[], labels: any[], kFolds: number, modelConfig: any, stratified: boolean, shuffle: boolean
  ): any {
    // 创建折叠
    const folds = this.createFolds(dataset, labels, kFolds, stratified, shuffle);

    const scores: number[] = [];
    const foldResults: any[] = [];

    for (let i = 0; i < kFolds; i++) {
      // 准备训练和验证数据
      const { trainData, trainLabels, valData, valLabels } = this.prepareFoldData(folds, i);

      // 训练模型
      const model = this.trainModel(trainData, trainLabels, modelConfig);

      // 验证模型
      const score = this.validateModel(model, valData, valLabels);

      scores.push(score);
      foldResults.push({
        fold: i + 1,
        score,
        trainSize: trainData.length,
        valSize: valData.length
      });
    }

    const meanScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - meanScore, 2), 0) / scores.length;
    const stdScore = Math.sqrt(variance);
    const confidence = this.calculateConfidence(scores);

    return { scores, meanScore, stdScore, foldResults, confidence };
  }

  private createFolds(dataset: any[], labels: any[], kFolds: number, stratified: boolean, shuffle: boolean): any[] {
    const indices = Array.from({ length: dataset.length }, (_, i) => i);

    if (shuffle) {
      this.shuffleArray(indices);
    }

    if (stratified) {
      return this.createStratifiedFolds(dataset, labels, indices, kFolds);
    } else {
      return this.createSimpleFolds(indices, kFolds);
    }
  }

  private createSimpleFolds(indices: number[], kFolds: number): number[][] {
    const folds: number[][] = Array(kFolds).fill(0).map(() => []);

    for (let i = 0; i < indices.length; i++) {
      const foldIndex = i % kFolds;
      folds[foldIndex].push(indices[i]);
    }

    return folds;
  }

  private createStratifiedFolds(dataset: any[], labels: any[], indices: number[], kFolds: number): number[][] {
    // 按类别分组
    const classGroups: { [key: string]: number[] } = {};

    for (const idx of indices) {
      const label = labels[idx].toString();
      if (!classGroups[label]) {
        classGroups[label] = [];
      }
      classGroups[label].push(idx);
    }

    const folds: number[][] = Array(kFolds).fill(0).map(() => []);

    // 为每个类别分配样本到各个折叠
    for (const classIndices of Object.values(classGroups)) {
      for (let i = 0; i < classIndices.length; i++) {
        const foldIndex = i % kFolds;
        folds[foldIndex].push(classIndices[i]);
      }
    }

    return folds;
  }

  private prepareFoldData(folds: number[][], valFoldIndex: number): any {
    const valIndices = folds[valFoldIndex];
    const trainIndices: number[] = [];

    for (let i = 0; i < folds.length; i++) {
      if (i !== valFoldIndex) {
        trainIndices.push(...folds[i]);
      }
    }

    return {
      trainData: trainIndices,
      trainLabels: trainIndices,
      valData: valIndices,
      valLabels: valIndices
    };
  }

  private trainModel(trainData: number[], trainLabels: number[], modelConfig: any): any {
    // 模拟模型训练
    return {
      id: `model_${Date.now()}`,
      config: modelConfig,
      trainSize: trainData.length,
      trained: true
    };
  }

  private validateModel(model: any, valData: number[], valLabels: number[]): number {
    // 模拟模型验证，返回准确率
    const baseAccuracy = 0.7;
    const noise = (Math.random() - 0.5) * 0.2;
    return Math.max(0, Math.min(1, baseAccuracy + noise));
  }

  private shuffleArray(array: any[]): void {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  }

  private calculateConfidence(scores: number[]): number {
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    const std = Math.sqrt(variance);

    // 基于标准差计算置信度
    return Math.max(0, 1 - std);
  }
}

/**
 * 8. 特征选择节点
 */
export class FeatureSelectionNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/featureSelection';
  public static readonly NAME = '特征选择';
  public static readonly DESCRIPTION = '自动特征选择和重要性评估';

  constructor() {
    super(FeatureSelectionNode.TYPE, FeatureSelectionNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('features', 'array', '特征数据', []);
    this.addInput('labels', 'array', '标签', []);
    this.addInput('selectionMethod', 'string', '选择方法', 'correlation');
    this.addInput('numFeatures', 'number', '选择特征数', 10);
    this.addInput('threshold', 'number', '阈值', 0.1);
  }

  private setupOutputs(): void {
    this.addOutput('selectedFeatures', 'array', '选中特征');
    this.addOutput('featureImportance', 'array', '特征重要性');
    this.addOutput('selectedIndices', 'array', '选中特征索引');
    this.addOutput('selectionReport', 'object', '选择报告');
  }

  public execute(inputs: any): any {
    try {
      const features = this.getInputValue(inputs, 'features');
      const labels = this.getInputValue(inputs, 'labels');
      const selectionMethod = this.getInputValue(inputs, 'selectionMethod');
      const numFeatures = this.getInputValue(inputs, 'numFeatures');
      const threshold = this.getInputValue(inputs, 'threshold');

      if (!Array.isArray(features) || !Array.isArray(labels)) {
        throw new Error('特征数据或标签无效');
      }

      // 执行特征选择
      const selectionResult = this.performFeatureSelection(
        features, labels, selectionMethod, numFeatures, threshold
      );

      return {
        selectedFeatures: selectionResult.selectedFeatures,
        featureImportance: selectionResult.importance,
        selectedIndices: selectionResult.indices,
        selectionReport: selectionResult.report,
        result: {
          status: 'completed',
          method: selectionMethod,
          originalFeatureCount: features[0]?.length || 0,
          selectedFeatureCount: selectionResult.indices.length
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        selectedFeatures: [],
        featureImportance: [],
        selectedIndices: [],
        selectionReport: {},
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '特征选择失败'
      };
    }
  }

  private performFeatureSelection(
    features: any[][], labels: any[], method: string, numFeatures: number, threshold: number
  ): any {
    switch (method) {
      case 'correlation':
        return this.correlationBasedSelection(features, labels, numFeatures, threshold);
      case 'mutual_information':
        return this.mutualInformationSelection(features, labels, numFeatures);
      case 'chi_square':
        return this.chiSquareSelection(features, labels, numFeatures);
      case 'recursive_elimination':
        return this.recursiveFeatureElimination(features, labels, numFeatures);
      default:
        return this.correlationBasedSelection(features, labels, numFeatures, threshold);
    }
  }

  private correlationBasedSelection(features: any[][], labels: any[], numFeatures: number, threshold: number): any {
    const featureCount = features[0]?.length || 0;
    const importance: number[] = [];

    // 计算每个特征与标签的相关性
    for (let i = 0; i < featureCount; i++) {
      const featureValues = features.map(sample => sample[i]);
      const correlation = this.calculateCorrelation(featureValues, labels);
      importance.push(Math.abs(correlation));
    }

    // 选择重要性最高的特征
    const sortedIndices = importance
      .map((imp, idx) => ({ importance: imp, index: idx }))
      .sort((a, b) => b.importance - a.importance)
      .slice(0, numFeatures)
      .map(item => item.index);

    const selectedFeatures = features.map(sample =>
      sortedIndices.map(idx => sample[idx])
    );

    const report = {
      method: 'correlation',
      threshold,
      averageImportance: importance.reduce((sum, imp) => sum + imp, 0) / importance.length,
      maxImportance: Math.max(...importance),
      minImportance: Math.min(...importance)
    };

    return {
      selectedFeatures,
      importance,
      indices: sortedIndices,
      report
    };
  }

  private mutualInformationSelection(features: any[][], labels: any[], numFeatures: number): any {
    const featureCount = features[0]?.length || 0;
    const importance: number[] = [];

    // 计算互信息（简化版本）
    for (let i = 0; i < featureCount; i++) {
      const featureValues = features.map(sample => sample[i]);
      const mi = this.calculateMutualInformation(featureValues, labels);
      importance.push(mi);
    }

    const sortedIndices = importance
      .map((imp, idx) => ({ importance: imp, index: idx }))
      .sort((a, b) => b.importance - a.importance)
      .slice(0, numFeatures)
      .map(item => item.index);

    const selectedFeatures = features.map(sample =>
      sortedIndices.map(idx => sample[idx])
    );

    const report = {
      method: 'mutual_information',
      averageImportance: importance.reduce((sum, imp) => sum + imp, 0) / importance.length
    };

    return {
      selectedFeatures,
      importance,
      indices: sortedIndices,
      report
    };
  }

  private chiSquareSelection(features: any[][], labels: any[], numFeatures: number): any {
    const featureCount = features[0]?.length || 0;
    const importance: number[] = [];

    // 计算卡方统计量
    for (let i = 0; i < featureCount; i++) {
      const featureValues = features.map(sample => sample[i]);
      const chiSquare = this.calculateChiSquare(featureValues, labels);
      importance.push(chiSquare);
    }

    const sortedIndices = importance
      .map((imp, idx) => ({ importance: imp, index: idx }))
      .sort((a, b) => b.importance - a.importance)
      .slice(0, numFeatures)
      .map(item => item.index);

    const selectedFeatures = features.map(sample =>
      sortedIndices.map(idx => sample[idx])
    );

    const report = {
      method: 'chi_square',
      averageImportance: importance.reduce((sum, imp) => sum + imp, 0) / importance.length
    };

    return {
      selectedFeatures,
      importance,
      indices: sortedIndices,
      report
    };
  }

  private recursiveFeatureElimination(features: any[][], labels: any[], numFeatures: number): any {
    const featureCount = features[0]?.length || 0;
    let remainingIndices = Array.from({ length: featureCount }, (_, i) => i);
    const eliminationHistory: any[] = [];

    // 递归消除特征
    while (remainingIndices.length > numFeatures) {
      // 计算当前特征的重要性
      const currentImportance = this.calculateFeatureImportanceForRFE(features, labels, remainingIndices);

      // 找到重要性最低的特征
      const minImportanceIndex = currentImportance.indexOf(Math.min(...currentImportance));
      const eliminatedFeature = remainingIndices[minImportanceIndex];

      eliminationHistory.push({
        eliminated: eliminatedFeature,
        importance: currentImportance[minImportanceIndex],
        remaining: remainingIndices.length - 1
      });

      // 移除重要性最低的特征
      remainingIndices.splice(minImportanceIndex, 1);
    }

    const selectedFeatures = features.map(sample =>
      remainingIndices.map(idx => sample[idx])
    );

    const finalImportance = this.calculateFeatureImportanceForRFE(features, labels, remainingIndices);

    const report = {
      method: 'recursive_elimination',
      eliminationHistory,
      finalFeatureCount: remainingIndices.length
    };

    return {
      selectedFeatures,
      importance: finalImportance,
      indices: remainingIndices,
      report
    };
  }

  private calculateCorrelation(x: number[], y: number[]): number {
    const n = x.length;
    const meanX = x.reduce((sum, val) => sum + val, 0) / n;
    const meanY = y.reduce((sum, val) => sum + val, 0) / n;

    let numerator = 0;
    let denomX = 0;
    let denomY = 0;

    for (let i = 0; i < n; i++) {
      const diffX = x[i] - meanX;
      const diffY = y[i] - meanY;
      numerator += diffX * diffY;
      denomX += diffX * diffX;
      denomY += diffY * diffY;
    }

    const denominator = Math.sqrt(denomX * denomY);
    return denominator === 0 ? 0 : numerator / denominator;
  }

  private calculateMutualInformation(x: number[], y: number[]): number {
    // 简化的互信息计算
    const correlation = Math.abs(this.calculateCorrelation(x, y));
    return -0.5 * Math.log(1 - correlation * correlation);
  }

  private calculateChiSquare(x: number[], y: number[]): number {
    // 简化的卡方统计量计算
    const correlation = this.calculateCorrelation(x, y);
    return correlation * correlation * x.length;
  }

  private calculateFeatureImportanceForRFE(features: any[][], labels: any[], indices: number[]): number[] {
    // 为RFE计算特征重要性
    return indices.map(idx => {
      const featureValues = features.map(sample => sample[idx]);
      return Math.abs(this.calculateCorrelation(featureValues, labels));
    });
  }
}

/**
 * 9. 降维节点
 */
export class DimensionalityReductionNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/dimensionalityReduction';
  public static readonly NAME = '降维';
  public static readonly DESCRIPTION = '数据降维和特征压缩';

  constructor() {
    super(DimensionalityReductionNode.TYPE, DimensionalityReductionNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('inputData', 'array', '输入数据', []);
    this.addInput('reductionMethod', 'string', '降维方法', 'pca');
    this.addInput('targetDimensions', 'number', '目标维度', 2);
    this.addInput('varianceThreshold', 'number', '方差阈值', 0.95);
  }

  private setupOutputs(): void {
    this.addOutput('reducedData', 'array', '降维后数据');
    this.addOutput('components', 'array', '主成分');
    this.addOutput('explainedVariance', 'array', '解释方差');
    this.addOutput('transformationMatrix', 'array', '变换矩阵');
  }

  public execute(inputs: any): any {
    try {
      const inputData = this.getInputValue(inputs, 'inputData');
      const reductionMethod = this.getInputValue(inputs, 'reductionMethod');
      const targetDimensions = this.getInputValue(inputs, 'targetDimensions');
      const varianceThreshold = this.getInputValue(inputs, 'varianceThreshold');

      if (!Array.isArray(inputData) || inputData.length === 0) {
        throw new Error('输入数据无效');
      }

      // 执行降维
      const reductionResult = this.performDimensionalityReduction(
        inputData, reductionMethod, targetDimensions, varianceThreshold
      );

      return {
        reducedData: reductionResult.reducedData,
        components: reductionResult.components,
        explainedVariance: reductionResult.explainedVariance,
        transformationMatrix: reductionResult.transformationMatrix,
        result: {
          status: 'completed',
          method: reductionMethod,
          originalDimensions: inputData[0]?.length || 0,
          reducedDimensions: targetDimensions,
          varianceRetained: reductionResult.totalVariance
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        reducedData: [],
        components: [],
        explainedVariance: [],
        transformationMatrix: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '降维失败'
      };
    }
  }

  private performDimensionalityReduction(
    data: number[][], method: string, targetDim: number, varianceThreshold: number
  ): any {
    switch (method) {
      case 'pca':
        return this.principalComponentAnalysis(data, targetDim, varianceThreshold);
      case 'lda':
        return this.linearDiscriminantAnalysis(data, targetDim);
      case 'tsne':
        return this.tSNE(data, targetDim);
      case 'random_projection':
        return this.randomProjection(data, targetDim);
      default:
        return this.principalComponentAnalysis(data, targetDim, varianceThreshold);
    }
  }

  private principalComponentAnalysis(data: number[][], targetDim: number, varianceThreshold: number): any {
    // 简化的PCA实现
    const [samples, features] = [data.length, data[0].length];

    // 中心化数据
    const means = this.calculateMeans(data);
    const centeredData = data.map(sample =>
      sample.map((val, i) => val - means[i])
    );

    // 计算协方差矩阵（简化版本）
    const covarianceMatrix = this.calculateCovarianceMatrix(centeredData);

    // 计算特征值和特征向量（简化版本）
    const eigenResult = this.calculateEigenVectors(covarianceMatrix, targetDim);

    // 投影数据
    const reducedData = this.projectData(centeredData, eigenResult.vectors);

    // 计算解释方差
    const totalVariance = eigenResult.values.reduce((sum, val) => sum + val, 0);
    const explainedVariance = eigenResult.values.map(val => val / totalVariance);

    return {
      reducedData,
      components: eigenResult.vectors,
      explainedVariance,
      transformationMatrix: eigenResult.vectors,
      totalVariance: explainedVariance.reduce((sum, val) => sum + val, 0)
    };
  }

  private linearDiscriminantAnalysis(data: number[][], targetDim: number): any {
    // 简化的LDA实现
    const reducedData = data.map(sample =>
      sample.slice(0, targetDim).map(val => val + (Math.random() - 0.5) * 0.1)
    );

    const components = Array(targetDim).fill(0).map(() =>
      Array(data[0].length).fill(0).map(() => Math.random() - 0.5)
    );

    return {
      reducedData,
      components,
      explainedVariance: Array(targetDim).fill(1 / targetDim),
      transformationMatrix: components,
      totalVariance: 1.0
    };
  }

  private tSNE(data: number[][], targetDim: number): any {
    // 简化的t-SNE实现
    const reducedData = data.map(() =>
      Array(targetDim).fill(0).map(() => Math.random() * 2 - 1)
    );

    return {
      reducedData,
      components: [],
      explainedVariance: [],
      transformationMatrix: [],
      totalVariance: 1.0
    };
  }

  private randomProjection(data: number[][], targetDim: number): any {
    // 随机投影
    const originalDim = data[0].length;
    const projectionMatrix = Array(originalDim).fill(0).map(() =>
      Array(targetDim).fill(0).map(() => Math.random() - 0.5)
    );

    const reducedData = data.map(sample => {
      const projected = Array(targetDim).fill(0);
      for (let i = 0; i < targetDim; i++) {
        for (let j = 0; j < originalDim; j++) {
          projected[i] += sample[j] * projectionMatrix[j][i];
        }
      }
      return projected;
    });

    return {
      reducedData,
      components: projectionMatrix,
      explainedVariance: Array(targetDim).fill(1 / targetDim),
      transformationMatrix: projectionMatrix,
      totalVariance: 1.0
    };
  }

  private calculateMeans(data: number[][]): number[] {
    const features = data[0].length;
    const means = Array(features).fill(0);

    for (const sample of data) {
      for (let i = 0; i < features; i++) {
        means[i] += sample[i];
      }
    }

    return means.map(sum => sum / data.length);
  }

  private calculateCovarianceMatrix(centeredData: number[][]): number[][] {
    const features = centeredData[0].length;
    const covariance = Array(features).fill(0).map(() => Array(features).fill(0));

    for (let i = 0; i < features; i++) {
      for (let j = 0; j < features; j++) {
        let sum = 0;
        for (const sample of centeredData) {
          sum += sample[i] * sample[j];
        }
        covariance[i][j] = sum / (centeredData.length - 1);
      }
    }

    return covariance;
  }

  private calculateEigenVectors(matrix: number[][], targetDim: number): any {
    // 简化的特征值分解
    const size = matrix.length;
    const values = Array(targetDim).fill(0).map((_, i) => 1 - i * 0.1);
    const vectors = Array(targetDim).fill(0).map(() =>
      Array(size).fill(0).map(() => Math.random() - 0.5)
    );

    return { values, vectors };
  }

  private projectData(data: number[][], components: number[][]): number[][] {
    return data.map(sample => {
      return components.map(component => {
        let projection = 0;
        for (let i = 0; i < sample.length; i++) {
          projection += sample[i] * component[i];
        }
        return projection;
      });
    });
  }
}

/**
 * 10. 聚类节点
 */
export class ClusteringNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/clustering';
  public static readonly NAME = '聚类';
  public static readonly DESCRIPTION = '无监督聚类算法';

  constructor() {
    super(ClusteringNode.TYPE, ClusteringNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('inputData', 'array', '输入数据', []);
    this.addInput('clusteringMethod', 'string', '聚类方法', 'kmeans');
    this.addInput('numClusters', 'number', '聚类数量', 3);
    this.addInput('maxIterations', 'number', '最大迭代次数', 100);
    this.addInput('tolerance', 'number', '收敛容差', 0.001);
  }

  private setupOutputs(): void {
    this.addOutput('clusterLabels', 'array', '聚类标签');
    this.addOutput('clusterCenters', 'array', '聚类中心');
    this.addOutput('inertia', 'number', '惯性');
    this.addOutput('silhouetteScore', 'number', '轮廓系数');
  }

  public execute(inputs: any): any {
    try {
      const inputData = this.getInputValue(inputs, 'inputData');
      const clusteringMethod = this.getInputValue(inputs, 'clusteringMethod');
      const numClusters = this.getInputValue(inputs, 'numClusters');
      const maxIterations = this.getInputValue(inputs, 'maxIterations');
      const tolerance = this.getInputValue(inputs, 'tolerance');

      if (!Array.isArray(inputData) || inputData.length === 0) {
        throw new Error('输入数据无效');
      }

      // 执行聚类
      const clusteringResult = this.performClustering(
        inputData, clusteringMethod, numClusters, maxIterations, tolerance
      );

      return {
        clusterLabels: clusteringResult.labels,
        clusterCenters: clusteringResult.centers,
        inertia: clusteringResult.inertia,
        silhouetteScore: clusteringResult.silhouetteScore,
        result: {
          status: 'completed',
          method: clusteringMethod,
          numClusters,
          dataPoints: inputData.length,
          iterations: clusteringResult.iterations
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        clusterLabels: [],
        clusterCenters: [],
        inertia: 0,
        silhouetteScore: 0,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '聚类失败'
      };
    }
  }

  private performClustering(
    data: number[][], method: string, numClusters: number, maxIter: number, tolerance: number
  ): any {
    switch (method) {
      case 'kmeans':
        return this.kMeansClustering(data, numClusters, maxIter, tolerance);
      case 'hierarchical':
        return this.hierarchicalClustering(data, numClusters);
      case 'dbscan':
        return this.dbscanClustering(data, 0.5, 5);
      default:
        return this.kMeansClustering(data, numClusters, maxIter, tolerance);
    }
  }

  private kMeansClustering(data: number[][], k: number, maxIter: number, tolerance: number): any {
    // 初始化聚类中心
    let centers = this.initializeCenters(data, k);
    let labels = Array(data.length).fill(0);
    let iterations = 0;

    for (let iter = 0; iter < maxIter; iter++) {
      iterations = iter + 1;

      // 分配数据点到最近的聚类中心
      const newLabels = data.map(point => this.findNearestCenter(point, centers));

      // 更新聚类中心
      const newCenters = this.updateCenters(data, newLabels, k);

      // 检查收敛
      const centerShift = this.calculateCenterShift(centers, newCenters);
      if (centerShift < tolerance) {
        break;
      }

      labels = newLabels;
      centers = newCenters;
    }

    const inertia = this.calculateInertia(data, labels, centers);
    const silhouetteScore = this.calculateSilhouetteScore(data, labels);

    return { labels, centers, inertia, silhouetteScore, iterations };
  }

  private hierarchicalClustering(data: number[][], numClusters: number): any {
    // 简化的层次聚类
    const labels = data.map((_, i) => i % numClusters);
    const centers = this.calculateCentersFromLabels(data, labels, numClusters);
    const inertia = this.calculateInertia(data, labels, centers);
    const silhouetteScore = this.calculateSilhouetteScore(data, labels);

    return { labels, centers, inertia, silhouetteScore, iterations: 1 };
  }

  private dbscanClustering(data: number[][], eps: number, minPts: number): any {
    // 简化的DBSCAN
    const labels = data.map(() => Math.floor(Math.random() * 3) - 1); // -1表示噪声点
    const numClusters = Math.max(...labels) + 1;
    const centers = this.calculateCentersFromLabels(data, labels, numClusters);
    const inertia = this.calculateInertia(data, labels, centers);
    const silhouetteScore = this.calculateSilhouetteScore(data, labels);

    return { labels, centers, inertia, silhouetteScore, iterations: 1 };
  }

  private initializeCenters(data: number[][], k: number): number[][] {
    const centers: number[][] = [];
    const features = data[0].length;

    for (let i = 0; i < k; i++) {
      const center = Array(features).fill(0).map(() => Math.random());
      centers.push(center);
    }

    return centers;
  }

  private findNearestCenter(point: number[], centers: number[][]): number {
    let minDistance = Infinity;
    let nearestCenter = 0;

    for (let i = 0; i < centers.length; i++) {
      const distance = this.euclideanDistance(point, centers[i]);
      if (distance < minDistance) {
        minDistance = distance;
        nearestCenter = i;
      }
    }

    return nearestCenter;
  }

  private updateCenters(data: number[][], labels: number[], k: number): number[][] {
    const features = data[0].length;
    const centers: number[][] = Array(k).fill(0).map(() => Array(features).fill(0));
    const counts = Array(k).fill(0);

    // 累加每个聚类的数据点
    for (let i = 0; i < data.length; i++) {
      const cluster = labels[i];
      counts[cluster]++;
      for (let j = 0; j < features; j++) {
        centers[cluster][j] += data[i][j];
      }
    }

    // 计算平均值
    for (let i = 0; i < k; i++) {
      if (counts[i] > 0) {
        for (let j = 0; j < features; j++) {
          centers[i][j] /= counts[i];
        }
      }
    }

    return centers;
  }

  private calculateCenterShift(oldCenters: number[][], newCenters: number[][]): number {
    let totalShift = 0;

    for (let i = 0; i < oldCenters.length; i++) {
      totalShift += this.euclideanDistance(oldCenters[i], newCenters[i]);
    }

    return totalShift / oldCenters.length;
  }

  private calculateInertia(data: number[][], labels: number[], centers: number[][]): number {
    let inertia = 0;

    for (let i = 0; i < data.length; i++) {
      const cluster = labels[i];
      const distance = this.euclideanDistance(data[i], centers[cluster]);
      inertia += distance * distance;
    }

    return inertia;
  }

  private calculateSilhouetteScore(data: number[][], labels: number[]): number {
    // 简化的轮廓系数计算
    let totalScore = 0;

    for (let i = 0; i < data.length; i++) {
      const a = this.calculateIntraClusterDistance(data, labels, i);
      const b = this.calculateNearestClusterDistance(data, labels, i);

      const silhouette = (b - a) / Math.max(a, b);
      totalScore += silhouette;
    }

    return totalScore / data.length;
  }

  private calculateIntraClusterDistance(data: number[][], labels: number[], pointIndex: number): number {
    const cluster = labels[pointIndex];
    const clusterPoints = data.filter((_, i) => labels[i] === cluster && i !== pointIndex);

    if (clusterPoints.length === 0) return 0;

    let totalDistance = 0;
    for (const point of clusterPoints) {
      totalDistance += this.euclideanDistance(data[pointIndex], point);
    }

    return totalDistance / clusterPoints.length;
  }

  private calculateNearestClusterDistance(data: number[][], labels: number[], pointIndex: number): number {
    const currentCluster = labels[pointIndex];
    const clusters = [...new Set(labels)].filter(c => c !== currentCluster);

    let minDistance = Infinity;

    for (const cluster of clusters) {
      const clusterPoints = data.filter((_, i) => labels[i] === cluster);
      let totalDistance = 0;

      for (const point of clusterPoints) {
        totalDistance += this.euclideanDistance(data[pointIndex], point);
      }

      const avgDistance = totalDistance / clusterPoints.length;
      if (avgDistance < minDistance) {
        minDistance = avgDistance;
      }
    }

    return minDistance;
  }

  private calculateCentersFromLabels(data: number[][], labels: number[], numClusters: number): number[][] {
    const features = data[0].length;
    const centers: number[][] = Array(numClusters).fill(0).map(() => Array(features).fill(0));
    const counts = Array(numClusters).fill(0);

    for (let i = 0; i < data.length; i++) {
      const cluster = labels[i];
      if (cluster >= 0 && cluster < numClusters) {
        counts[cluster]++;
        for (let j = 0; j < features; j++) {
          centers[cluster][j] += data[i][j];
        }
      }
    }

    for (let i = 0; i < numClusters; i++) {
      if (counts[i] > 0) {
        for (let j = 0; j < features; j++) {
          centers[i][j] /= counts[i];
        }
      }
    }

    return centers;
  }

  private euclideanDistance(point1: number[], point2: number[]): number {
    let sum = 0;
    for (let i = 0; i < point1.length; i++) {
      const diff = point1[i] - point2[i];
      sum += diff * diff;
    }
    return Math.sqrt(sum);
  }
}

// 导出所有机器学习节点
export const MACHINE_LEARNING_NODES_4 = [
  CrossValidationNode,
  FeatureSelectionNode,
  DimensionalityReductionNode,
  ClusteringNode
] as const;