/**
 * 自然语言处理节点实现 - 批次3.3
 * 实现7个高级自然语言处理节点
 */

import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 文本分类结果接口
 */
export interface TextClassificationResult {
  label: string;
  confidence: number;
  category: string;
  subcategory?: string;
}

/**
 * 命名实体识别结果接口
 */
export interface NamedEntityResult {
  text: string;
  label: string;
  start: number;
  end: number;
  confidence: number;
  entityId?: string;
}

/**
 * 情感分析结果接口
 */
export interface SentimentResult {
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  scores: {
    positive: number;
    negative: number;
    neutral: number;
  };
  emotions?: {
    joy: number;
    anger: number;
    fear: number;
    sadness: number;
    surprise: number;
    disgust: number;
  };
}

/**
 * 文本摘要结果接口
 */
export interface SummarizationResult {
  summary: string;
  originalLength: number;
  summaryLength: number;
  compressionRatio: number;
  keyPoints: string[];
  confidence: number;
}

/**
 * 机器翻译结果接口
 */
export interface TranslationResult {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence: number;
  alternatives?: Array<{
    text: string;
    confidence: number;
  }>;
}

/**
 * 问答系统结果接口
 */
export interface QuestionAnsweringResult {
  answer: string;
  confidence: number;
  startIndex: number;
  endIndex: number;
  context: string;
  supportingEvidence?: string[];
}

/**
 * 文本生成结果接口
 */
export interface TextGenerationResult {
  generatedText: string;
  prompt: string;
  completionTokens: number;
  totalTokens: number;
  finishReason: 'stop' | 'length' | 'content_filter';
  alternatives?: string[];
}

/**
 * 文本分类节点
 * 对文本进行分类和标签预测
 */
export class TextClassificationNode extends VisualScriptNode {
  constructor() {
    super('TextClassification', '文本分类');
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('text', 'string', '输入文本', '');
    this.addInput('modelId', 'string', '分类模型ID', '');
    this.addInput('classificationTask', 'string', '分类任务', 'sentiment'); // sentiment, topic, intent, spam
    this.addInput('topK', 'number', '返回前K个结果', 3);
    this.addInput('confidenceThreshold', 'number', '置信度阈值', 0.5);
  }

  private setupOutputs(): void {
    this.addOutput('classifications', 'array', '分类结果');
    this.addOutput('topClassification', 'object', '最佳分类');
    this.addOutput('confidence', 'number', '最高置信度');
    this.addOutput('allScores', 'object', '所有分数');
    this.addOutput('onClassified', 'boolean', '分类完成');
    this.addOutput('onError', 'boolean', '分类失败');
  }

  public execute(inputs?: any): any {
    try {
      const text = inputs?.text || this.getInputValue('text');
      const modelId = inputs?.modelId || this.getInputValue('modelId');
      const classificationTask = inputs?.classificationTask || this.getInputValue('classificationTask');
      const topK = inputs?.topK || this.getInputValue('topK');
      const confidenceThreshold = inputs?.confidenceThreshold || this.getInputValue('confidenceThreshold');

      if (!text) {
        throw new Error('输入文本不能为空');
      }

      Debug.log('TextClassificationNode', `开始文本分类: ${classificationTask}`);

      const result = this.classifyText(text, modelId, classificationTask, topK, confidenceThreshold);

      Debug.log('TextClassificationNode', `文本分类完成，最佳分类: ${result.topClassification.label}`);

      return {
        classifications: result.classifications,
        topClassification: result.topClassification,
        confidence: result.topClassification.confidence,
        allScores: result.allScores,
        onClassified: true,
        onError: false
      };

    } catch (error) {
      Debug.error('TextClassificationNode', '文本分类失败:', error);
      return {
        classifications: [],
        topClassification: {},
        confidence: 0,
        allScores: {},
        onClassified: false,
        onError: true
      };
    }
  }

  private classifyText(text: string, modelId: string, task: string, topK: number, threshold: number): any {
    // 根据任务类型生成不同的分类标签
    const taskLabels = {
      'sentiment': ['positive', 'negative', 'neutral'],
      'topic': ['technology', 'sports', 'politics', 'entertainment', 'business', 'health'],
      'intent': ['question', 'request', 'complaint', 'compliment', 'information'],
      'spam': ['spam', 'not_spam'],
      'emotion': ['joy', 'anger', 'fear', 'sadness', 'surprise', 'disgust', 'neutral']
    };

    const labels = taskLabels[task as keyof typeof taskLabels] || taskLabels.sentiment;
    const classifications: TextClassificationResult[] = [];

    // 生成分类结果
    for (const label of labels) {
      const confidence = Math.random();
      if (confidence >= threshold) {
        classifications.push({
          label,
          confidence,
          category: task,
          subcategory: task === 'topic' ? `${label}_subcategory` : undefined
        });
      }
    }

    // 按置信度排序并取前K个
    classifications.sort((a, b) => b.confidence - a.confidence);
    const topClassifications = classifications.slice(0, topK);

    // 生成所有分数对象
    const allScores: any = {};
    for (const label of labels) {
      allScores[label] = Math.random();
    }

    return {
      classifications: topClassifications,
      topClassification: topClassifications[0] || { label: 'unknown', confidence: 0, category: task },
      allScores
    };
  }
}

/**
 * 命名实体识别节点
 * 识别文本中的命名实体
 */
export class NamedEntityRecognitionNode extends VisualScriptNode {
  constructor() {
    super('NamedEntityRecognition', '命名实体识别');
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('text', 'string', '输入文本', '');
    this.addInput('modelId', 'string', 'NER模型ID', '');
    this.addInput('entityTypes', 'array', '实体类型', ['PERSON', 'ORG', 'LOC', 'MISC']);
    this.addInput('confidenceThreshold', 'number', '置信度阈值', 0.7);
    this.addInput('mergeEntities', 'boolean', '合并实体', true);
  }

  private setupOutputs(): void {
    this.addOutput('entities', 'array', '识别实体');
    this.addOutput('entityCount', 'number', '实体数量');
    this.addOutput('entityTypes', 'object', '实体类型统计');
    this.addOutput('annotatedText', 'string', '标注文本');
    this.addOutput('onRecognized', 'boolean', '识别完成');
    this.addOutput('onError', 'boolean', '识别失败');
  }

  public execute(inputs?: any): any {
    try {
      const text = inputs?.text || this.getInputValue('text');
      const modelId = inputs?.modelId || this.getInputValue('modelId');
      const entityTypes = inputs?.entityTypes || this.getInputValue('entityTypes');
      const confidenceThreshold = inputs?.confidenceThreshold || this.getInputValue('confidenceThreshold');
      const mergeEntities = inputs?.mergeEntities !== undefined ? inputs.mergeEntities : this.getInputValue('mergeEntities');

      if (!text) {
        throw new Error('输入文本不能为空');
      }

      Debug.log('NamedEntityRecognitionNode', '开始命名实体识别');

      const result = this.recognizeEntities(text, modelId, entityTypes, confidenceThreshold, mergeEntities);

      Debug.log('NamedEntityRecognitionNode', `实体识别完成，识别到 ${result.entities.length} 个实体`);

      return {
        entities: result.entities,
        entityCount: result.entities.length,
        entityTypes: result.entityTypes,
        annotatedText: result.annotatedText,
        onRecognized: true,
        onError: false
      };

    } catch (error) {
      Debug.error('NamedEntityRecognitionNode', '实体识别失败:', error);
      return {
        entities: [],
        entityCount: 0,
        entityTypes: {},
        annotatedText: '',
        onRecognized: false,
        onError: true
      };
    }
  }

  private recognizeEntities(text: string, modelId: string, entityTypes: string[], threshold: number, mergeEntities: boolean): any {
    // 模拟实体识别
    const words = text.split(/\s+/);
    const entities: NamedEntityResult[] = [];
    const entityTypeCount: any = {};
    let annotatedText = text;

    // 初始化实体类型计数
    for (const type of entityTypes) {
      entityTypeCount[type] = 0;
    }

    // 模拟识别过程
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      
      // 随机决定是否为实体
      if (Math.random() > 0.7) { // 30% 概率为实体
        const entityType = entityTypes[Math.floor(Math.random() * entityTypes.length)];
        const confidence = threshold + Math.random() * (1 - threshold);
        
        const startIndex = text.indexOf(word, i > 0 ? entities[entities.length - 1]?.end || 0 : 0);
        const endIndex = startIndex + word.length;

        const entity: NamedEntityResult = {
          text: word,
          label: entityType,
          start: startIndex,
          end: endIndex,
          confidence,
          entityId: `entity_${entities.length}`
        };

        entities.push(entity);
        entityTypeCount[entityType]++;

        // 在标注文本中添加标记
        const annotation = `[${word}:${entityType}]`;
        annotatedText = annotatedText.replace(word, annotation);
      }
    }

    return {
      entities,
      entityTypes: entityTypeCount,
      annotatedText
    };
  }
}

/**
 * 情感分析节点（增强版）
 * 对文本进行详细的情感和情绪分析
 */
export class SentimentAnalysisNode extends VisualScriptNode {
  constructor() {
    super('SentimentAnalysis', '情感分析');
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('text', 'string', '输入文本', '');
    this.addInput('modelId', 'string', '情感模型ID', '');
    this.addInput('analysisLevel', 'string', '分析级别', 'sentence'); // sentence, document, aspect
    this.addInput('includeEmotions', 'boolean', '包含情绪', true);
    this.addInput('language', 'string', '语言', 'auto');
  }

  private setupOutputs(): void {
    this.addOutput('sentiment', 'object', '情感结果');
    this.addOutput('emotions', 'object', '情绪分析');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('aspectSentiments', 'array', '方面情感');
    this.addOutput('onAnalyzed', 'boolean', '分析完成');
    this.addOutput('onError', 'boolean', '分析失败');
  }

  public execute(inputs?: any): any {
    try {
      const text = inputs?.text || this.getInputValue('text');
      const modelId = inputs?.modelId || this.getInputValue('modelId');
      const analysisLevel = inputs?.analysisLevel || this.getInputValue('analysisLevel');
      const includeEmotions = inputs?.includeEmotions !== undefined ? inputs.includeEmotions : this.getInputValue('includeEmotions');
      const language = inputs?.language || this.getInputValue('language');

      if (!text) {
        throw new Error('输入文本不能为空');
      }

      Debug.log('SentimentAnalysisNode', `开始情感分析: ${analysisLevel}`);

      const result = this.analyzeSentiment(text, modelId, analysisLevel, includeEmotions, language);

      Debug.log('SentimentAnalysisNode', `情感分析完成，情感: ${result.sentiment.sentiment}`);

      return {
        sentiment: result.sentiment,
        emotions: result.emotions,
        confidence: result.sentiment.confidence,
        aspectSentiments: result.aspectSentiments,
        onAnalyzed: true,
        onError: false
      };

    } catch (error) {
      Debug.error('SentimentAnalysisNode', '情感分析失败:', error);
      return {
        sentiment: {},
        emotions: {},
        confidence: 0,
        aspectSentiments: [],
        onAnalyzed: false,
        onError: true
      };
    }
  }

  private analyzeSentiment(text: string, modelId: string, level: string, includeEmotions: boolean, language: string): any {
    // 模拟情感分析
    const sentiments = ['positive', 'negative', 'neutral'];
    const sentiment = sentiments[Math.floor(Math.random() * sentiments.length)];

    const sentimentResult: SentimentResult = {
      sentiment: sentiment as any,
      confidence: Math.random() * 0.3 + 0.7,
      scores: {
        positive: Math.random(),
        negative: Math.random(),
        neutral: Math.random()
      }
    };

    // 归一化分数
    const total = sentimentResult.scores.positive + sentimentResult.scores.negative + sentimentResult.scores.neutral;
    sentimentResult.scores.positive /= total;
    sentimentResult.scores.negative /= total;
    sentimentResult.scores.neutral /= total;

    // 情绪分析
    const emotions = includeEmotions ? {
      joy: Math.random(),
      anger: Math.random(),
      fear: Math.random(),
      sadness: Math.random(),
      surprise: Math.random(),
      disgust: Math.random()
    } : {};

    // 方面情感分析
    const aspectSentiments = level === 'aspect' ? [
      { aspect: 'service', sentiment: 'positive', confidence: 0.8 },
      { aspect: 'price', sentiment: 'negative', confidence: 0.7 },
      { aspect: 'quality', sentiment: 'positive', confidence: 0.9 }
    ] : [];

    return {
      sentiment: sentimentResult,
      emotions,
      aspectSentiments
    };
  }
}

/**
 * 文本摘要节点
 * 生成文本的摘要和关键点
 */
export class TextSummarizationNode extends VisualScriptNode {
  constructor() {
    super('TextSummarization', '文本摘要');
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('text', 'string', '输入文本', '');
    this.addInput('modelId', 'string', '摘要模型ID', '');
    this.addInput('summaryType', 'string', '摘要类型', 'extractive'); // extractive, abstractive
    this.addInput('maxLength', 'number', '最大长度', 150);
    this.addInput('minLength', 'number', '最小长度', 50);
    this.addInput('keyPointsCount', 'number', '关键点数量', 3);
  }

  private setupOutputs(): void {
    this.addOutput('summary', 'string', '摘要文本');
    this.addOutput('keyPoints', 'array', '关键点');
    this.addOutput('summaryStats', 'object', '摘要统计');
    this.addOutput('originalLength', 'number', '原文长度');
    this.addOutput('onSummarized', 'boolean', '摘要完成');
    this.addOutput('onError', 'boolean', '摘要失败');
  }

  public execute(inputs?: any): any {
    try {
      const text = inputs?.text || this.getInputValue('text');
      const modelId = inputs?.modelId || this.getInputValue('modelId');
      const summaryType = inputs?.summaryType || this.getInputValue('summaryType');
      const maxLength = inputs?.maxLength || this.getInputValue('maxLength');
      const minLength = inputs?.minLength || this.getInputValue('minLength');
      const keyPointsCount = inputs?.keyPointsCount || this.getInputValue('keyPointsCount');

      if (!text) {
        throw new Error('输入文本不能为空');
      }

      Debug.log('TextSummarizationNode', `开始文本摘要: ${summaryType}`);

      const result = this.summarizeText(text, modelId, summaryType, maxLength, minLength, keyPointsCount);

      Debug.log('TextSummarizationNode', `文本摘要完成，压缩比: ${result.summaryStats.compressionRatio}`);

      return {
        summary: result.summary,
        keyPoints: result.keyPoints,
        summaryStats: result.summaryStats,
        originalLength: text.length,
        onSummarized: true,
        onError: false
      };

    } catch (error) {
      Debug.error('TextSummarizationNode', '文本摘要失败:', error);
      return {
        summary: '',
        keyPoints: [],
        summaryStats: {},
        originalLength: 0,
        onSummarized: false,
        onError: true
      };
    }
  }

  private summarizeText(text: string, modelId: string, type: string, maxLength: number, minLength: number, keyPointsCount: number): any {
    // 模拟文本摘要
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const summaryLength = Math.min(maxLength, Math.max(minLength, Math.floor(text.length * 0.3)));

    let summary: string;
    if (type === 'extractive') {
      // 抽取式摘要：选择重要句子
      const selectedSentences = sentences.slice(0, Math.min(3, sentences.length));
      summary = selectedSentences.join('. ') + '.';
    } else {
      // 生成式摘要：生成新文本
      summary = `This is an abstractive summary of the input text. It captures the main ideas and presents them in a concise manner.`;
    }

    // 确保摘要长度在范围内
    if (summary.length > maxLength) {
      summary = summary.substring(0, maxLength - 3) + '...';
    }

    // 生成关键点
    const keyPoints = [];
    for (let i = 0; i < keyPointsCount; i++) {
      keyPoints.push(`Key point ${i + 1}: Important information extracted from the text.`);
    }

    const summaryStats = {
      originalLength: text.length,
      summaryLength: summary.length,
      compressionRatio: summary.length / text.length,
      sentenceCount: sentences.length,
      summarySentenceCount: summary.split(/[.!?]+/).length - 1
    };

    return {
      summary,
      keyPoints,
      summaryStats
    };
  }
}
