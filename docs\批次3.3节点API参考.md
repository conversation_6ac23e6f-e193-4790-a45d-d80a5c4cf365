# 批次3.3节点API参考

## 概述

本文档提供批次3.3所有25个节点的详细API参考，包括输入输出参数、方法签名和使用示例。

## 深度学习节点API

### DeepLearningModelNode

```typescript
class DeepLearningModelNode extends DeepLearningNode {
  static readonly TYPE = 'ai/deepLearningModel';
  static readonly NAME = '深度学习模型';
  static readonly DESCRIPTION = '创建和管理深度学习模型';

  // 输入参数
  interface Inputs {
    modelId: string;           // 模型ID
    modelType: string;         // 模型类型
    inputSize: number;         // 输入维度
    outputSize: number;        // 输出维度
    hiddenLayers: number[];    // 隐藏层配置
    activation: string;        // 激活函数
  }

  // 输出结果
  interface Outputs {
    model: object;             // 模型对象
    modelInfo: object;         // 模型信息
    result: object;            // 执行结果
    success: boolean;          // 成功标志
    error: string;             // 错误信息
  }

  execute(inputs: Inputs): Outputs;
}
```

### NeuralNetworkNode

```typescript
class NeuralNetworkNode extends DeepLearningNode {
  static readonly TYPE = 'ai/neuralNetwork';
  
  interface Inputs {
    inputData: number[];       // 输入数据
    weights: number[][][];     // 权重矩阵
    biases: number[][];        // 偏置向量
    activationFunction: string; // 激活函数
  }

  interface Outputs {
    output: number[];          // 网络输出
    activations: number[][];   // 各层激活值
    result: object;
    success: boolean;
    error: string;
  }
}
```

### ConvolutionalNetworkNode

```typescript
class ConvolutionalNetworkNode extends DeepLearningNode {
  static readonly TYPE = 'ai/convolutionalNetwork';
  
  interface Inputs {
    inputImage: number[][];    // 输入图像
    filters: number[][][];     // 卷积核
    kernelSize: number;        // 核大小
    stride: number;            // 步长
    padding: string;           // 填充方式
    poolingType: string;       // 池化类型
    poolingSize: number;       // 池化大小
  }

  interface Outputs {
    featureMaps: number[][][]; // 特征图
    pooledMaps: number[][][];  // 池化后特征图
    outputShape: number[];     // 输出形状
    result: object;
    success: boolean;
    error: string;
  }
}
```

### TransformerModelNode

```typescript
class TransformerModelNode extends DeepLearningNode {
  static readonly TYPE = 'ai/transformerModel';
  
  interface Inputs {
    inputSequence: number[][]; // 输入序列
    numHeads: number;          // 注意力头数
    dModel: number;            // 模型维度
    dff: number;               // 前馈网络维度
    numLayers: number;         // 层数
    maxLength: number;         // 最大长度
  }

  interface Outputs {
    encodedSequence: number[][]; // 编码序列
    attentionWeights: number[][][]; // 注意力权重
    positionEncoding: number[][]; // 位置编码
    result: object;
    success: boolean;
    error: string;
  }
}
```

### ActivationFunctionNode

```typescript
class ActivationFunctionNode extends DeepLearningNode {
  static readonly TYPE = 'ai/activationFunction';
  
  interface Inputs {
    inputData: number[];       // 输入数据
    activationType: string;    // 激活函数类型
    alpha: number;             // 参数alpha
  }

  interface Outputs {
    activatedData: number[];   // 激活后数据
    derivative: number[];      // 导数
    result: object;
    success: boolean;
    error: string;
  }

  // 支持的激活函数类型
  static ACTIVATION_TYPES = [
    'relu', 'leaky_relu', 'sigmoid', 
    'tanh', 'swish', 'gelu'
  ];
}
```

## 机器学习节点API

### ReinforcementLearningNode

```typescript
class ReinforcementLearningNode extends MachineLearningNode {
  static readonly TYPE = 'ml/reinforcementLearning';
  
  interface Inputs {
    state: string;             // 当前状态
    action: string;            // 动作
    reward: number;            // 奖励
    nextState: string;         // 下一状态
    availableActions: string[]; // 可用动作
    learningRate: number;      // 学习率
    discountFactor: number;    // 折扣因子
    explorationRate: number;   // 探索率
  }

  interface Outputs {
    bestAction: string;        // 最佳动作
    qValue: number;            // Q值
    qTable: object;            // Q表
    explorationAction: boolean; // 是否探索动作
    result: object;
    success: boolean;
    error: string;
  }
}
```

### ModelValidationNode

```typescript
class ModelValidationNode extends MachineLearningNode {
  static readonly TYPE = 'ml/modelValidation';
  
  interface Inputs {
    model: object;             // 模型
    testData: any[];           // 测试数据
    testLabels: any[];         // 测试标签
    validationMethod: string;  // 验证方法
    metrics: string[];         // 评估指标
  }

  interface Outputs {
    validationResults: object; // 验证结果
    confusionMatrix: number[][]; // 混淆矩阵
    performanceMetrics: object; // 性能指标
    validationReport: object;  // 验证报告
    result: object;
    success: boolean;
    error: string;
  }

  // 支持的评估指标
  static METRICS = [
    'accuracy', 'precision', 'recall', 
    'f1_score', 'specificity', 'auc'
  ];
}
```

### ClusteringNode

```typescript
class ClusteringNode extends MachineLearningNode {
  static readonly TYPE = 'ml/clustering';
  
  interface Inputs {
    inputData: number[][];     // 输入数据
    clusteringMethod: string;  // 聚类方法
    numClusters: number;       // 聚类数量
    maxIterations: number;     // 最大迭代次数
    tolerance: number;         // 收敛容差
  }

  interface Outputs {
    clusterLabels: number[];   // 聚类标签
    clusterCenters: number[][]; // 聚类中心
    inertia: number;           // 惯性
    silhouetteScore: number;   // 轮廓系数
    result: object;
    success: boolean;
    error: string;
  }

  // 支持的聚类方法
  static CLUSTERING_METHODS = [
    'kmeans', 'hierarchical', 'dbscan'
  ];
}
```

## 节点注册表API

### Batch33NodesRegistry

```typescript
class Batch33NodesRegistry {
  static getInstance(): Batch33NodesRegistry;
  
  // 获取节点类
  getNodeClass(type: string): any;
  
  // 获取节点元数据
  getNodeMetadata(type: string): any;
  
  // 创建节点实例
  createNode(type: string): VisualScriptNode | null;
  
  // 获取所有注册的节点类型
  getAllRegisteredNodeTypes(): string[];
  
  // 获取节点分类统计
  getNodeCategoryStats(): {
    '深度学习': number;
    '机器学习': number;
    '总计': number;
  };
}
```

## 编辑器集成API

### Batch33NodesIntegration

```typescript
class Batch33NodesIntegration {
  constructor(nodeEditor: NodeEditor);
  
  // 创建节点实例
  createNode(nodeType: string): VisualScriptNode | null;
  
  // 获取已注册的节点类型
  getRegisteredNodeTypes(): string[];
  
  // 获取节点统计信息
  getNodeStats(): {
    totalNodes: number;
    deepLearningNodes: number;
    machineLearningNodes: number;
    categories: string[];
  };
  
  // 验证节点集成
  validateIntegration(): boolean;
  
  // 获取节点使用示例
  getNodeExamples(): any;
}
```

## 工具函数API

### 验证和检查函数

```typescript
// 验证批次3.3实现
function validateBatch33Implementation(): boolean;

// 创建批次3.3节点
function createBatch33Node(nodeType: string): any;

// 检查是否为批次3.3节点类型
function isBatch33NodeType(nodeType: string): boolean;

// 检查是否为深度学习节点
function isDeepLearningNode(nodeType: string): boolean;

// 检查是否为机器学习节点
function isMachineLearningNode(nodeType: string): boolean;
```

### 常量定义

```typescript
// 深度学习节点类型
const DEEP_LEARNING_NODE_TYPES = [
  'ai/deepLearningModel',
  'ai/neuralNetwork',
  'ai/convolutionalNetwork',
  // ... 其他类型
] as const;

// 机器学习节点类型
const MACHINE_LEARNING_NODE_TYPES = [
  'ml/reinforcementLearning',
  'ml/federatedLearning',
  'ml/transferLearning',
  // ... 其他类型
] as const;

// 节点统计
const BATCH33_STATS = {
  DEEP_LEARNING_COUNT: 15,
  MACHINE_LEARNING_COUNT: 10,
  TOTAL_COUNT: 25,
  CATEGORIES: 2
} as const;
```

## 错误处理

### 错误类型

```typescript
interface NodeError {
  code: string;              // 错误代码
  message: string;           // 错误消息
  details?: any;             // 错误详情
}

// 常见错误代码
const ERROR_CODES = {
  INVALID_INPUT: 'INVALID_INPUT',
  MODEL_NOT_FOUND: 'MODEL_NOT_FOUND',
  COMPUTATION_FAILED: 'COMPUTATION_FAILED',
  INSUFFICIENT_DATA: 'INSUFFICIENT_DATA',
  CONVERGENCE_FAILED: 'CONVERGENCE_FAILED'
};
```

### 错误处理最佳实践

```typescript
try {
  const result = node.execute(inputs);
  if (!result.success) {
    console.error('节点执行失败:', result.error);
    // 处理错误
  }
} catch (error) {
  console.error('节点执行异常:', error);
  // 异常处理
}
```

## 性能优化

### 内存管理

```typescript
// 释放大型数据结构
function cleanup(node: VisualScriptNode): void {
  // 清理缓存
  // 释放内存
}
```

### 批处理优化

```typescript
// 批量处理数据
function batchProcess(
  node: VisualScriptNode, 
  batchData: any[], 
  batchSize: number = 32
): any[] {
  const results = [];
  for (let i = 0; i < batchData.length; i += batchSize) {
    const batch = batchData.slice(i, i + batchSize);
    const batchResult = node.execute({ inputData: batch });
    results.push(...batchResult.output);
  }
  return results;
}
```

## 版本兼容性

### API版本

- **当前版本**: v1.0.0
- **最低兼容版本**: DL引擎 v3.3+
- **TypeScript版本**: 4.5+

### 迁移指南

从旧版本迁移时，请注意：
1. 检查节点类型名称变更
2. 更新输入输出参数格式
3. 验证新的错误处理机制

---

**文档版本**: v1.0.0  
**最后更新**: 2025年7月3日  
**维护者**: DL引擎开发团队
